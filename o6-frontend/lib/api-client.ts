// HTTP Client for external backend API integration
import { getSession } from 'next-auth/react'
import { API_CONFIG, buildUrl } from './api-config'

// Types for API responses
export interface APIResponse<T = any> {
  success: boolean
  data?: T
  error?: {
    message: string
    code: string
    details?: any
  }
  pagination?: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

// HTTP methods
type HttpMethod = 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE'

// Request options
interface RequestOptions {
  method?: HttpMethod
  headers?: Record<string, string>
  body?: any
  timeout?: number
  requireAuth?: boolean
  retries?: number
}

class APIClient {
  private baseUrl: string

  constructor() {
    this.baseUrl = buildUrl('')
  }

  // Get authentication headers
  private async getAuthHeaders(): Promise<Record<string, string>> {
    const session = await getSession()
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    }

    if (session?.accessToken) {
      headers.Authorization = `Bearer ${session.accessToken}`
    }

    return headers
  }

  // Make HTTP request with retry logic
  private async makeRequest<T>(
    endpoint: string,
    options: RequestOptions = {}
  ): Promise<APIResponse<T>> {
    const {
      method = 'GET',
      headers = {},
      body,
      timeout = API_CONFIG.TIMEOUT.DEFAULT,
      requireAuth = true,
      retries = API_CONFIG.RETRY.MAX_ATTEMPTS
    } = options

    const url = buildUrl(endpoint)
    
    // Prepare headers
    const requestHeaders = requireAuth 
      ? { ...await this.getAuthHeaders(), ...headers }
      : { 'Content-Type': 'application/json', ...headers }

    // Prepare request config
    const requestConfig: RequestInit = {
      method,
      headers: requestHeaders,
      signal: AbortSignal.timeout(timeout)
    }

    // Add body for non-GET requests
    if (body && method !== 'GET') {
      requestConfig.body = typeof body === 'string' ? body : JSON.stringify(body)
    }

    // Retry logic
    for (let attempt = 1; attempt <= retries; attempt++) {
      try {
        const response = await fetch(url, requestConfig)
        
        // Handle different response types
        const contentType = response.headers.get('content-type')
        let responseData: any

        if (contentType?.includes('application/json')) {
          responseData = await response.json()
        } else {
          responseData = await response.text()
        }

        // Handle successful responses
        if (response.ok) {
          return {
            success: true,
            data: responseData
          }
        }

        // Handle error responses
        return {
          success: false,
          error: {
            message: responseData.message || `HTTP ${response.status}: ${response.statusText}`,
            code: responseData.code || `HTTP_${response.status}`,
            details: responseData
          }
        }

      } catch (error) {
        // If this is the last attempt, return the error
        if (attempt === retries) {
          return {
            success: false,
            error: {
              message: error instanceof Error ? error.message : 'Network error',
              code: 'NETWORK_ERROR',
              details: error
            }
          }
        }

        // Wait before retrying
        await new Promise(resolve => 
          setTimeout(resolve, API_CONFIG.RETRY.DELAY * Math.pow(API_CONFIG.RETRY.BACKOFF_FACTOR, attempt - 1))
        )
      }
    }

    // This should never be reached, but TypeScript requires it
    return {
      success: false,
      error: {
        message: 'Unexpected error',
        code: 'UNKNOWN_ERROR'
      }
    }
  }

  // HTTP method helpers
  async get<T>(endpoint: string, options: Omit<RequestOptions, 'method'> = {}): Promise<APIResponse<T>> {
    return this.makeRequest<T>(endpoint, { ...options, method: 'GET' })
  }

  async post<T>(endpoint: string, body?: any, options: Omit<RequestOptions, 'method' | 'body'> = {}): Promise<APIResponse<T>> {
    return this.makeRequest<T>(endpoint, { ...options, method: 'POST', body })
  }

  async put<T>(endpoint: string, body?: any, options: Omit<RequestOptions, 'method' | 'body'> = {}): Promise<APIResponse<T>> {
    return this.makeRequest<T>(endpoint, { ...options, method: 'PUT', body })
  }

  async patch<T>(endpoint: string, body?: any, options: Omit<RequestOptions, 'method' | 'body'> = {}): Promise<APIResponse<T>> {
    return this.makeRequest<T>(endpoint, { ...options, method: 'PATCH', body })
  }

  async delete<T>(endpoint: string, options: Omit<RequestOptions, 'method'> = {}): Promise<APIResponse<T>> {
    return this.makeRequest<T>(endpoint, { ...options, method: 'DELETE' })
  }

  // File upload helper
  async uploadFile(endpoint: string, file: File, additionalData?: Record<string, any>): Promise<APIResponse> {
    const formData = new FormData()
    formData.append('file', file)
    
    if (additionalData) {
      Object.entries(additionalData).forEach(([key, value]) => {
        formData.append(key, typeof value === 'string' ? value : JSON.stringify(value))
      })
    }

    const headers = await this.getAuthHeaders()
    delete headers['Content-Type'] // Let browser set it for FormData

    return this.makeRequest(endpoint, {
      method: 'POST',
      headers,
      body: formData,
      timeout: API_CONFIG.TIMEOUT.UPLOAD
    })
  }
}

// Export singleton instance
export const apiClient = new APIClient()

// Export class for testing or custom instances
export { APIClient }

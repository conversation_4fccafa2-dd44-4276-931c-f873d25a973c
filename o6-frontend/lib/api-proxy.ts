// API Proxy for Next.js API routes to external backend
import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/app/auth'

// Configuration for external backend
const EXTERNAL_API_BASE = process.env.EXTERNAL_API_URL || 'http://localhost:3001/api'

// Helper to get user session and organization
export async function getAuthContext() {
  const session = await auth()
  
  if (!session?.user?.id) {
    return { error: 'Unauthorized', status: 401 }
  }

  return {
    userId: session.user.id,
    userEmail: session.user.email,
    session
  }
}

// Proxy request to external backend
export async function proxyToBackend(
  request: NextRequest,
  endpoint: string,
  options: {
    method?: string
    requireAuth?: boolean
    transformRequest?: (body: any) => any
    transformResponse?: (data: any) => any
  } = {}
) {
  const {
    method = request.method,
    requireAuth = true,
    transformRequest,
    transformResponse
  } = options

  try {
    // Check authentication if required
    if (requireAuth) {
      const authContext = await getAuthContext()
      if ('error' in authContext) {
        return NextResponse.json(
          { error: authContext.error },
          { status: authContext.status }
        )
      }
    }

    // Get request body
    let body = null
    if (method !== 'GET' && method !== 'DELETE') {
      try {
        body = await request.json()
        if (transformRequest) {
          body = transformRequest(body)
        }
      } catch (error) {
        // Body might be empty or invalid JSON
      }
    }

    // Build external API URL
    const url = new URL(endpoint, EXTERNAL_API_BASE)
    
    // Copy query parameters
    const searchParams = new URL(request.url).searchParams
    searchParams.forEach((value, key) => {
      url.searchParams.append(key, value)
    })

    // Prepare headers for external API
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    }

    // Add authentication header if available
    if (requireAuth) {
      const authContext = await getAuthContext()
      if ('session' in authContext && authContext.session) {
        // You might need to get a backend token here
        // For now, we'll pass the user ID
        headers['X-User-ID'] = authContext.userId
        headers['X-User-Email'] = authContext.userEmail || ''
      }
    }

    // Make request to external backend
    const response = await fetch(url.toString(), {
      method,
      headers,
      body: body ? JSON.stringify(body) : undefined,
    })

    // Get response data
    let responseData
    const contentType = response.headers.get('content-type')
    
    if (contentType?.includes('application/json')) {
      responseData = await response.json()
    } else {
      responseData = await response.text()
    }

    // Transform response if needed
    if (transformResponse && response.ok) {
      responseData = transformResponse(responseData)
    }

    // Return response with same status
    return NextResponse.json(responseData, { status: response.status })

  } catch (error) {
    console.error('Proxy error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Helper for common CRUD operations
export const createCRUDProxy = (baseEndpoint: string) => ({
  // GET /api/resource
  async list(request: NextRequest) {
    return proxyToBackend(request, baseEndpoint, {
      method: 'GET'
    })
  },

  // POST /api/resource
  async create(request: NextRequest) {
    return proxyToBackend(request, baseEndpoint, {
      method: 'POST'
    })
  },

  // GET /api/resource/[id]
  async get(request: NextRequest, id: string) {
    return proxyToBackend(request, `${baseEndpoint}/${id}`, {
      method: 'GET'
    })
  },

  // PUT /api/resource/[id]
  async update(request: NextRequest, id: string) {
    return proxyToBackend(request, `${baseEndpoint}/${id}`, {
      method: 'PUT'
    })
  },

  // DELETE /api/resource/[id]
  async delete(request: NextRequest, id: string) {
    return proxyToBackend(request, `${baseEndpoint}/${id}`, {
      method: 'DELETE'
    })
  }
})

// Example usage for specific resources
export const programsProxy = createCRUDProxy('/programs')
export const clientsProxy = createCRUDProxy('/clients')
export const jobsProxy = createCRUDProxy('/jobs')
export const notesProxy = createCRUDProxy('/notes')

// Custom proxy for authentication endpoints
export const authProxy = {
  async login(request: NextRequest) {
    return proxyToBackend(request, '/auth/login', {
      method: 'POST',
      requireAuth: false
    })
  },

  async register(request: NextRequest) {
    return proxyToBackend(request, '/auth/register', {
      method: 'POST',
      requireAuth: false
    })
  },

  async logout(request: NextRequest) {
    return proxyToBackend(request, '/auth/logout', {
      method: 'POST'
    })
  }
}

// API Configuration for external backend integration
export const API_CONFIG = {
  // Base URLs for different environments
  BASE_URL: {
    development: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001/api',
    production: process.env.NEXT_PUBLIC_API_URL || 'https://localhost:8080/',
    staging: process.env.NEXT_PUBLIC_API_URL || 'https://staging-api.com/api'
  },
  
  // API endpoints
  ENDPOINTS: {
    // Authentication
    AUTH: {
      LOGIN: '/auth/login',
      LOGOUT: '/auth/logout',
      REFRESH: '/auth/refresh',
      REGISTER: '/auth/register',
      VERIFY: '/auth/verify'
    },
    
    // User management
    USERS: {
      PROFILE: '/users/profile',
      UPDATE: '/users/update',
      LIST: '/users',
      BY_ID: (id: string) => `/users/${id}`
    },
    
    // Organizations
    ORGANIZATIONS: {
      LIST: '/organizations',
      CREATE: '/organizations',
      BY_ID: (id: string) => `/organizations/${id}`,
      UPDATE: (id: string) => `/organizations/${id}`,
      DELETE: (id: string) => `/organizations/${id}`
    },
    
    // HCM Module
    HCM: {
      PROGRAMS: '/hcm/programs',
      OBJECTIVES: '/hcm/objectives',
      TASKS: '/hcm/tasks',
      CANDIDATES: '/hcm/candidates',
      JOBS: '/hcm/jobs'
    },
    
    // CRM Module
    CRM: {
      CLIENTS: '/crm/clients',
      CONTACTS: '/crm/contacts',
      DEALS: '/crm/deals',
      ACTIVITIES: '/crm/activities'
    },
    
    // Notes and Documents
    NOTES: {
      LIST: '/notes',
      CREATE: '/notes',
      BY_ID: (id: string) => `/notes/${id}`,
      UPDATE: (id: string) => `/notes/${id}`,
      DELETE: (id: string) => `/notes/${id}`,
      BY_TASK: (taskId: string) => `/notes/task/${taskId}`
    },
    
    // File uploads
    FILES: {
      UPLOAD: '/files/upload',
      DOWNLOAD: (id: string) => `/files/${id}`,
      DELETE: (id: string) => `/files/${id}`
    }
  },
  
  // Request timeouts
  TIMEOUT: {
    DEFAULT: 10000, // 10 seconds
    UPLOAD: 60000,  // 1 minute for file uploads
    LONG_RUNNING: 120000 // 2 minutes for complex operations
  },
  
  // Retry configuration
  RETRY: {
    MAX_ATTEMPTS: 3,
    DELAY: 1000, // 1 second
    BACKOFF_FACTOR: 2
  }
}

// Get the appropriate base URL for current environment
export const getBaseUrl = (): string => {
  const env = process.env.NODE_ENV as keyof typeof API_CONFIG.BASE_URL
  return API_CONFIG.BASE_URL[env] || API_CONFIG.BASE_URL.development
}

// Build full URL for an endpoint
export const buildUrl = (endpoint: string): string => {
  const baseUrl = getBaseUrl()
  return `${baseUrl}${endpoint}`
}

// Backend service layer for external API integration
import { apiClient } from './api-client'
import { API_CONFIG } from './api-config'
import type { 
  WorkspacePersona, 
  Program, 
  Objective, 
  TaskType, 
  Client, 
  Job, 
  Note 
} from '@/components/workspace/types'

// User and Organization types
export interface User {
  id: string
  email: string
  firstName: string
  lastName: string
  name: string
  organizationId: string
  persona: WorkspacePersona
  createdAt: Date
  updatedAt: Date
}

export interface Organization {
  id: string
  name: string
  industry: string
  size: string
  createdAt: Date
  updatedAt: Date
}

// Backend service class
export class BackendService {
  // Authentication methods
  async login(email: string, password: string) {
    const response = await apiClient.post(API_CONFIG.ENDPOINTS.AUTH.LOGIN, {
      email,
      password
    }, { requireAuth: false })

    if (response.success && response.data) {
      // Store tokens if needed
      return response.data
    }

    throw new Error(response.error?.message || 'Login failed')
  }

  async register(userData: {
    firstName: string
    lastName: string
    email: string
    organizationName: string
    industry: string
    size: string
  }) {
    const response = await apiClient.post(API_CONFIG.ENDPOINTS.AUTH.REGISTER, userData, {
      requireAuth: false
    })

    if (response.success) {
      return response.data
    }

    throw new Error(response.error?.message || 'Registration failed')
  }

  // User management
  async getCurrentUser(): Promise<User> {
    const response = await apiClient.get<User>(API_CONFIG.ENDPOINTS.USERS.PROFILE)
    
    if (response.success && response.data) {
      return {
        ...response.data,
        createdAt: new Date(response.data.createdAt),
        updatedAt: new Date(response.data.updatedAt)
      }
    }

    throw new Error(response.error?.message || 'Failed to fetch user profile')
  }

  async updateUser(userId: string, updates: Partial<User>): Promise<User> {
    const response = await apiClient.put<User>(API_CONFIG.ENDPOINTS.USERS.UPDATE, updates)
    
    if (response.success && response.data) {
      return {
        ...response.data,
        createdAt: new Date(response.data.createdAt),
        updatedAt: new Date(response.data.updatedAt)
      }
    }

    throw new Error(response.error?.message || 'Failed to update user')
  }

  // Organization management
  async getOrganization(orgId: string): Promise<Organization> {
    const response = await apiClient.get<Organization>(
      API_CONFIG.ENDPOINTS.ORGANIZATIONS.BY_ID(orgId)
    )
    
    if (response.success && response.data) {
      return {
        ...response.data,
        createdAt: new Date(response.data.createdAt),
        updatedAt: new Date(response.data.updatedAt)
      }
    }

    throw new Error(response.error?.message || 'Failed to fetch organization')
  }

  // HCM Module methods
  async getPrograms(persona?: WorkspacePersona): Promise<Program[]> {
    const params = persona ? `?persona=${persona}` : ''
    const response = await apiClient.get<Program[]>(`${API_CONFIG.ENDPOINTS.HCM.PROGRAMS}${params}`)
    
    if (response.success && response.data) {
      return response.data.map(program => ({
        ...program,
        createdAt: new Date(program.createdAt),
        updatedAt: new Date(program.updatedAt)
      }))
    }

    throw new Error(response.error?.message || 'Failed to fetch programs')
  }

  async createProgram(program: Omit<Program, 'id' | 'createdAt' | 'updatedAt'>): Promise<Program> {
    const response = await apiClient.post<Program>(API_CONFIG.ENDPOINTS.HCM.PROGRAMS, program)
    
    if (response.success && response.data) {
      return {
        ...response.data,
        createdAt: new Date(response.data.createdAt),
        updatedAt: new Date(response.data.updatedAt)
      }
    }

    throw new Error(response.error?.message || 'Failed to create program')
  }

  async getObjectives(programId?: string, persona?: WorkspacePersona): Promise<Objective[]> {
    const params = new URLSearchParams()
    if (programId) params.append('programId', programId)
    if (persona) params.append('persona', persona)
    
    const queryString = params.toString() ? `?${params.toString()}` : ''
    const response = await apiClient.get<Objective[]>(`${API_CONFIG.ENDPOINTS.HCM.OBJECTIVES}${queryString}`)
    
    if (response.success && response.data) {
      return response.data.map(objective => ({
        ...objective,
        createdAt: new Date(objective.createdAt),
        updatedAt: new Date(objective.updatedAt)
      }))
    }

    throw new Error(response.error?.message || 'Failed to fetch objectives')
  }

  async getTasks(objectiveId?: string, persona?: WorkspacePersona): Promise<TaskType[]> {
    const params = new URLSearchParams()
    if (objectiveId) params.append('objectiveId', objectiveId)
    if (persona) params.append('persona', persona)
    
    const queryString = params.toString() ? `?${params.toString()}` : ''
    const response = await apiClient.get<TaskType[]>(`${API_CONFIG.ENDPOINTS.HCM.TASKS}${queryString}`)
    
    if (response.success && response.data) {
      return response.data
    }

    throw new Error(response.error?.message || 'Failed to fetch tasks')
  }

  // CRM Module methods
  async getClients(): Promise<Client[]> {
    const response = await apiClient.get<Client[]>(API_CONFIG.ENDPOINTS.CRM.CLIENTS)
    
    if (response.success && response.data) {
      return response.data.map(client => ({
        ...client,
        createdAt: new Date(client.createdAt),
        updatedAt: new Date(client.updatedAt)
      }))
    }

    throw new Error(response.error?.message || 'Failed to fetch clients')
  }

  async createClient(client: Omit<Client, 'id' | 'createdAt' | 'updatedAt' | 'orgId' | 'openJobs' | 'totalPlacements'>): Promise<Client> {
    const response = await apiClient.post<Client>(API_CONFIG.ENDPOINTS.CRM.CLIENTS, client)
    
    if (response.success && response.data) {
      return {
        ...response.data,
        createdAt: new Date(response.data.createdAt),
        updatedAt: new Date(response.data.updatedAt)
      }
    }

    throw new Error(response.error?.message || 'Failed to create client')
  }

  // Notes methods
  async getNotes(taskId: string): Promise<Note | null> {
    const response = await apiClient.get<Note>(API_CONFIG.ENDPOINTS.NOTES.BY_TASK(taskId))
    
    if (response.success && response.data) {
      const note = response.data
      return {
        ...note,
        lastEdited: new Date(note.lastEdited),
        ...(note.type === 'meeting' && note.date && { date: new Date(note.date) })
      }
    }

    // Return null if no notes found (not an error)
    if (response.error?.code === 'NOT_FOUND') {
      return null
    }

    throw new Error(response.error?.message || 'Failed to fetch notes')
  }

  async createNote(note: Omit<Note, 'id' | 'lastEdited'>): Promise<Note> {
    const response = await apiClient.post<Note>(API_CONFIG.ENDPOINTS.NOTES.CREATE, note)
    
    if (response.success && response.data) {
      const createdNote = response.data
      return {
        ...createdNote,
        lastEdited: new Date(createdNote.lastEdited),
        ...(createdNote.type === 'meeting' && createdNote.date && { date: new Date(createdNote.date) })
      }
    }

    throw new Error(response.error?.message || 'Failed to create note')
  }

  // File upload
  async uploadFile(file: File, metadata?: Record<string, any>) {
    const response = await apiClient.uploadFile(API_CONFIG.ENDPOINTS.FILES.UPLOAD, file, metadata)
    
    if (response.success) {
      return response.data
    }

    throw new Error(response.error?.message || 'Failed to upload file')
  }
}

// Export singleton instance
export const backendService = new BackendService()

// Export class for testing
export { BackendService }

import { auth } from "./app/auth"
import { NextResponse } from "next/server"
import type { NextRequest } from "next/server"

export default async function middleware(request: NextRequest) {
  // Get the session
  const session = await auth()

  // Define protected routes
  const protectedRoutes = [
    '/home',
    '/workspace',
    '/notes',
    '/profile',
    '/settings',
    '/crm',
    '/dashboard'
  ]

  // Define public routes (accessible without authentication)
  const publicRoutes = [
    '/',
    '/login',
    '/register',
    '/verify-request',
    '/auth',
    '/marketing'
  ]

  const { pathname } = request.nextUrl

  // Check if the current path is protected
  const isProtectedRoute = protectedRoutes.some(route =>
    pathname.startsWith(route)
  )

  // Check if the current path is public
  const isPublicRoute = publicRoutes.some(route =>
    pathname === route || pathname.startsWith(route)
  )

  // If it's a protected route and user is not authenticated
  if (isProtectedRoute && !session) {
    const loginUrl = new URL('/login', request.url)
    loginUrl.searchParams.set('callbackUrl', pathname)
    return NextResponse.redirect(loginUrl)
  }

  // If user is authenticated and trying to access auth pages, redirect to dashboard
  if (session && (pathname === '/login' || pathname === '/register')) {
    return NextResponse.redirect(new URL('/home/<USER>', request.url))
  }

  // If accessing root and authenticated, redirect to dashboard
  if (session && pathname === '/') {
    return NextResponse.redirect(new URL('/home/<USER>', request.url))
  }

  return NextResponse.next()
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
}
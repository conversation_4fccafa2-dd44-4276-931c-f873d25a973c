# O6 Frontend

A modular, AI-powered business suite designed to manage both Human Capital Management (HCM) and Client Relationship Management (CRM) processes.

## Overview

O6 provides a unified interface where:

- **HCM Modules** (Recruiting, HR, Benefits, L&D) focus on managing the employee lifecycle
- **CRM Modules** (initially Clients, with plans to expand) enable recruitment agencies to manage external client relationships

Key features include:

- A unified dashboard with role-based access
- Specialized Task Types with corresponding Note Templates
- Contextual AI Assistant that provides relevant help based on user's current activity
- Component-based UI built in Next.js using the App Router

## Prerequisites

- [Node.js](https://nodejs.org/) (v18 or later)
- [npm](https://www.npmjs.com/) (v8 or later) or [Yarn](https://yarnpkg.com/) (v1.22 or later)
- [Git](https://git-scm.com/)

## Installation

1. Clone the repository:

   ```bash
   git clone https://github.com/your-organization/o6-frontend.git
   cd o6-frontend
   ```

2. Install dependencies:

   ```bash
   npm install
   # OR
   yarn install
   ```

3. Set up environment variables:

   ```bash
   cp .env.example .env.local
   ```

   Edit `.env.local` to add any required environment variables.
   
   > **Note**: By default, the application runs in DEMO mode, which uses mock data. See [Demo Mode Documentation](./docs/demo-mode.md) for details.

## Running the Application

### Development Mode

Run the development server:

```bash
npm run dev
# OR
yarn dev
```

The application will be available at [http://localhost:3000](http://localhost:3000)

### Production Build

Build for production:

```bash
npm run build
# OR
yarn build
```

Start the production server:

```bash
npm run start
# OR
yarn start
```

## Testing

### Running Tests

```bash
# Run all tests
npm run test
# OR
yarn test

# Run tests in watch mode
npm run test:watch
# OR
yarn test:watch

# Run tests with coverage
npm run test:coverage
# OR
yarn test:coverage
```

## Project Structure

```bash
/app                  # Next.js app directory (App Router)
  ├── crm/            # CRM module routes
  ├── home/           # Home dashboard pages
  ├── settings/       # Settings pages
  └── workspace/      # HCM workspace routes

/components           # React components
  ├── clients/        # Client-related components
  ├── workspace/      # HCM components
  │   ├── ai-assistant/ # AI assistant components
  │   ├── mock-data/  # Mock data for development
  │   ├── task/       # Task-related components
  │   └── views/      # View components (Dashboard, Timeline, etc.)
  └── ui/             # Reusable UI components

/docs                 # Project documentation
/public               # Static assets
```

## Contributing

### Development Workflow

1. **Fork the repository**: Create your own fork of the project
2. **Create a feature branch**: `git checkout -b feature/your-feature-name`
3. **Make your changes**: Follow the code style guidelines
4. **Write tests**: Ensure your changes are covered by tests
5. **Run linting**: `npm run lint` or `yarn lint`
6. **Format code**: `npm run format` or `yarn format`
7. **Commit your changes**: Use conventional commit messages
8. **Submit a pull request**: Provide a clear description of your changes

### Code Style Guidelines

- Follow the [Airbnb JavaScript Style Guide](https://github.com/airbnb/javascript)
- Use TypeScript for type safety
- Write meaningful variable and function names
- Keep functions small and focused on a single task
- Document complex logic with comments

### Commit Message Convention

We use [Conventional Commits](https://www.conventionalcommits.org/):

```bash
<type>(<scope>): <description>

[optional body]

[optional footer]
```

Types:

- `feat`: A new feature
- `fix`: A bug fix
- `docs`: Documentation changes
- `style`: Code style changes (formatting, missing semi-colons, etc)
- `refactor`: Code changes that neither fix bugs nor add features
- `test`: Adding or fixing tests
- `chore`: Changes to the build process or auxiliary tools

### Pull Request Process

1. Update documentation if needed
2. Add tests for new features
3. Ensure CI passes all checks
4. Get at least one code review
5. Wait for approval before merging

## Documentation

- [Technical Design Document](./docs/o6.md)
- [API Documentation](./docs/api.md) (if available)
- [Component Documentation](./docs/components.md) (if available)

## License

This software is proprietary and may not be used, reproduced, or distributed without explicit permission from `O6 AI`.

## Acknowledgments

- List of contributors and acknowledgments

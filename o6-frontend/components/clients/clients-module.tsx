"use client"

import React, { useState } from "react"
import { getClients, getJob<PERSON>, create<PERSON><PERSON>, createJob, update<PERSON>lient } from "@/lib/data-service"
import { ClientList } from "./client-list"
import { ClientDetail } from "./client-detail"
import { ClientForm } from "./client-form"
import { JobForm } from "./job-form"
import type { Client, Job } from "../workspace/types"

type ClientsView = "list" | "detail" | "create" | "edit"

interface ClientsModuleProps {
  user?: any;
}

export function ClientsModule({ user }: ClientsModuleProps) {
  const [clients, setClients] = useState<Client[]>([])
  const [jobs, setJobs] = useState<Job[]>([])
  const [selectedClient, setSelectedClient] = useState<Client | null>(null)
  const [view, setView] = useState<ClientsView>("list")
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [jobFormOpen, setJobFormOpen] = useState(false)
  const [jobFormClientId, setJobFormClientId] = useState<string | null>(null)

  // Fetch clients and jobs on mount
  React.useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true)
        setError(null)
        
        const [fetchedClients, fetchedJobs] = await Promise.allSettled([
          getClients(),
          getJobs()
        ])
        
        if (fetchedClients.status === 'fulfilled' && Array.isArray(fetchedClients.value)) {
          setClients(fetchedClients.value)
        } else if (fetchedClients.status === 'rejected') {
          console.error('Failed to fetch clients:', fetchedClients.reason)
        }
        
        if (fetchedJobs.status === 'fulfilled' && Array.isArray(fetchedJobs.value)) {
          setJobs(fetchedJobs.value)
        } else if (fetchedJobs.status === 'rejected') {
          console.error('Failed to fetch jobs:', fetchedJobs.reason)
        }
      } catch (err) {
        console.error('Error fetching data:', err)
        setError('Failed to load data. Please try again.')
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [])

  const handleClientSelect = (client: Client) => {
    setSelectedClient(client)
    setView("detail")
  }

  const handleCreateClient = () => {
    setSelectedClient(null)
    setView("create")
  }

  const handleEditClient = (client: Client) => {
    setSelectedClient(client)
    setView("edit")
  }

  const handleSaveClient = async (clientData: Partial<Client>) => {
    try {
      setLoading(true)
      setError(null)

      if (view === "create") {
        // Create new client
        const newClient = await createClient({
          name: clientData.name || "",
          industry: clientData.industry || "",
          location: clientData.location || "",
          contactPerson: clientData.contactPerson || "",
          contactEmail: clientData.contactEmail || "",
          contactPhone: clientData.contactPhone || "",
          status: (clientData.status as "active" | "inactive" | "lead" | "prospect") || "lead",
          notes: clientData.notes || "",
        })

        setClients([newClient, ...clients])
        setSelectedClient(newClient)
        setView("detail")
      } else if (view === "edit" && selectedClient) {
        // Update existing client
        const updatedClient = await updateClient(selectedClient.id, clientData)

        setClients(clients.map((c) => (c.id === selectedClient.id ? updatedClient : c)))
        setSelectedClient(updatedClient)
        setView("detail")
      }
    } catch (err) {
      console.error('Error saving client:', err)
      setError('Failed to save client. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  const handleCreateJob = async (clientId: string) => {
    setJobFormClientId(clientId)
    setJobFormOpen(true)
  }

  const handleJobFormSubmit = async (jobData: Partial<Job>) => {
    try {
      if (!jobFormClientId) return

      // Get user's persona from user prop or default to 'recruiter'
      const persona = user?.persona || 'recruiter'
      
      const newJob = await createJob({
        title: jobData.title || "",
        content: jobData.content || "",
        tags: jobData.tags || [],
        department: jobData.department || "",
        location: jobData.location || "",
        salary: jobData.salary || "",
        experience: jobData.experience || "",
        persona,
        type: jobData.type || "job_description",
        status: jobData.status || "draft",
        category: jobData.category || "",
        clientId: jobFormClientId,
      })

      setJobs([newJob, ...jobs])
      setJobFormOpen(false)
      setJobFormClientId(null)
      console.log(`Created job for client: ${jobFormClientId}`, newJob)
    } catch (err) {
      console.error('Error creating job:', err)
      setError('Failed to create job. Please try again.')
    }
  }

  const handleSelectJob = (job: Job) => {
    // This would navigate to job detail view
    console.log(`Selected job: ${job.id}`)
  }

  const handleCancel = () => {
    if (selectedClient) {
      setView("detail")
    } else {
      setView("list")
    }
  }

  // Render the appropriate view
  const renderView = () => {
    if (loading) {
      return (
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-forest-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading clients...</p>
          </div>
        </div>
      )
    }

    if (error) {
      return (
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <p className="text-red-600 mb-4">{error}</p>
            <button 
              onClick={() => window.location.reload()} 
              className="px-4 py-2 bg-forest-600 text-white rounded hover:bg-forest-700"
            >
              Retry
            </button>
          </div>
        </div>
      )
    }

    switch (view) {
      case "list":
        return <ClientList clients={clients} onClientSelect={handleClientSelect} onCreateClient={handleCreateClient} />
      case "detail":
        return selectedClient ? (
          <ClientDetail
            client={selectedClient}
            jobs={jobs.filter(job => job.clientId === selectedClient.id)}
            onEditClient={handleEditClient}
            onCreateJob={handleCreateJob}
            onSelectJob={handleSelectJob}
          />
        ) : null
      case "create":
        return <ClientForm onSave={handleSaveClient} onCancel={handleCancel} />
      case "edit":
        return selectedClient ? (
          <ClientForm client={selectedClient} onSave={handleSaveClient} onCancel={handleCancel} />
        ) : null
      default:
        return null
    }
  }

  return (
    <div className="h-full overflow-auto">
      {renderView()}
      
      <JobForm
        open={jobFormOpen}
        onOpenChange={(open) => {
          setJobFormOpen(open)
          if (!open) {
            setJobFormClientId(null)
          }
        }}
        onSave={handleJobFormSubmit}
        clientId={jobFormClientId || ""}
        clientName={jobFormClientId ? clients.find(c => c.id === jobFormClientId)?.name || "Unknown Client" : ""}
      />
    </div>
  )
}


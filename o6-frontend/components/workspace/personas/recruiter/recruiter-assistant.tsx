"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { FileText, Filter, Users, Wand2, Building } from "lucide-react"
import type { WorkspaceItem, Client } from "../../types"

interface RecruiterAssistantProps {
  activeItem: WorkspaceItem | null
  activeClient?: Client | null
}

export function RecruiterAssistant({ activeItem, activeClient }: RecruiterAssistantProps) {
  return (
    <div className="p-4 space-y-4">
      <div className="flex items-start gap-2">
        <Users className="h-5 w-5 text-forest-600 mt-0.5" />
        <p className="text-sm text-gray-700">
          {!activeItem &&
            !activeClient &&
            "Hello! I'm your Recruiting Assistant. How can I help with your recruiting tasks today?"}

          {activeItem?.type === "job_description" &&
            `I see you're working on the "${activeItem.title}" job description. I can help enhance the requirements, suggest skills to add, or create a boolean search string.`}

          {activeItem?.type === "boolean_search" &&
            `Looking at your search string for "${activeItem.title}". I can help optimize this query, suggest additional keywords, or estimate the candidate pool size.`}

          {activeItem?.type === "candidate_profile" &&
            `Reviewing "${activeItem.title}". I can help assess skill match, suggest screening questions, or draft an outreach email.`}

          {activeClient &&
            `I see you're working with ${activeClient.name}. I can help you manage jobs for this client, suggest candidates from your talent pool, or draft client communications.`}
        </p>
      </div>

      <div>
        <h4 className="text-xs font-medium text-gray-500 mb-2">Suggested Actions</h4>
        <div className="space-y-1">
          {!activeItem && !activeClient ? (
            <>
              <Button
                variant="ghost"
                size="sm"
                className="w-full justify-start text-sm h-8 px-2 font-normal text-gray-700 hover:bg-cream-100 hover:text-forest-600"
              >
                <FileText className="h-4 w-4 mr-2 text-gray-500" />
                Create job description template
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className="w-full justify-start text-sm h-8 px-2 font-normal text-gray-700 hover:bg-cream-100 hover:text-forest-600"
              >
                <Filter className="h-4 w-4 mr-2 text-gray-500" />
                Optimize boolean search
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className="w-full justify-start text-sm h-8 px-2 font-normal text-gray-700 hover:bg-cream-100 hover:text-forest-600"
              >
                <Users className="h-4 w-4 mr-2 text-gray-500" />
                Screen candidate profile
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className="w-full justify-start text-sm h-8 px-2 font-normal text-gray-700 hover:bg-cream-100 hover:text-forest-600"
              >
                <Building className="h-4 w-4 mr-2 text-gray-500" />
                Manage client relationships
              </Button>
            </>
          ) : activeClient ? (
            <>
              <Button
                variant="ghost"
                size="sm"
                className="w-full justify-start text-sm h-8 px-2 font-normal text-gray-700 hover:bg-cream-100 hover:text-forest-600"
              >
                <FileText className="h-4 w-4 mr-2 text-gray-500" />
                Create job for {activeClient.name}
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className="w-full justify-start text-sm h-8 px-2 font-normal text-gray-700 hover:bg-cream-100 hover:text-forest-600"
              >
                <Users className="h-4 w-4 mr-2 text-gray-500" />
                Find candidates for {activeClient.name}
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className="w-full justify-start text-sm h-8 px-2 font-normal text-gray-700 hover:bg-cream-100 hover:text-forest-600"
              >
                <Wand2 className="h-4 w-4 mr-2 text-gray-500" />
                Draft client communication
              </Button>
            </>
          ) : (
            <>
              <Button
                variant="ghost"
                size="sm"
                className="w-full justify-start text-sm h-8 px-2 font-normal text-gray-700 hover:bg-cream-100 hover:text-forest-600"
              >
                <Wand2 className="h-4 w-4 mr-2 text-gray-500" />
                {activeItem?.type === "job_description" && "Enhance job description"}
                {activeItem?.type === "boolean_search" && "Optimize search string"}
                {activeItem?.type === "candidate_profile" && "Assess skill match"}
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className="w-full justify-start text-sm h-8 px-2 font-normal text-gray-700 hover:bg-cream-100 hover:text-forest-600"
              >
                <FileText className="h-4 w-4 mr-2 text-gray-500" />
                {activeItem?.type === "job_description" && "Generate boolean search"}
                {activeItem?.type === "boolean_search" && "Estimate candidate pool"}
                {activeItem?.type === "candidate_profile" && "Draft outreach email"}
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className="w-full justify-start text-sm h-8 px-2 font-normal text-gray-700 hover:bg-cream-100 hover:text-forest-600"
              >
                <Users className="h-4 w-4 mr-2 text-gray-500" />
                {activeItem?.type === "job_description" && "Find similar candidates"}
                {activeItem?.type === "boolean_search" && "Add missing keywords"}
                {activeItem?.type === "candidate_profile" && "Suggest screening questions"}
              </Button>
            </>
          )}
        </div>
      </div>

      <div>
        <h4 className="text-xs font-medium text-gray-500 mb-2">Recruiting Insights</h4>
        <div className="bg-forest-100 p-3 rounded-md text-sm border border-forest-200">
          {!activeItem && !activeClient ? (
            <p className="text-gray-700">
              Your average time-to-hire is currently 24 days, which is 15% faster than industry average. Technical roles
              take 30% longer to fill than non-technical roles.
            </p>
          ) : activeClient ? (
            <p className="text-gray-700">
              {activeClient.name} has {activeClient.openJobs} open positions with an average time-to-fill of 28 days.
              Their most successful placements have been in the {activeClient.industry} sector.
            </p>
          ) : (
            <p className="text-gray-700">
              Based on current market trends, this role may be challenging to fill. Consider emphasizing remote work
              options and competitive benefits to attract more qualified candidates.
            </p>
          )}
        </div>
      </div>
    </div>
  )
}


"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>r, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "lucide-react"
import type { WorkspaceItem } from "../../types"

interface HRGeneralistAssistantProps {
  activeItem: WorkspaceItem | null
}

export function HRGeneralistAssistant({ activeItem }: HRGeneralistAssistantProps) {
  return (
    <div className="p-4 space-y-4">
      <div className="flex items-start gap-2">
        <User className="h-5 w-5 text-primary mt-0.5" />
        <p className="text-sm">
          {!activeItem && "Hello! I'm your HR Assistant. How can I help with your HR management tasks today?"}
          {activeItem?.itemType === "employee_record" &&
            `I see you're viewing ${activeItem.title}'s employee record. Would you like me to help with updating information or generating reports?`}
          {activeItem?.itemType === "policy_document" &&
            `Looking at the ${activeItem.title} policy. I can help with updates, distribution, or compliance tracking.`}
          {activeItem?.itemType === "compliance_report" &&
            `Reviewing the ${activeItem.title} report. Need help with completion, submission, or follow-up actions?`}
        </p>
      </div>

      <div>
        <h4 className="text-xs font-medium text-muted-foreground mb-2">Suggested Actions</h4>
        <div className="space-y-1">
          {!activeItem ? (
            <>
              <Button variant="ghost" size="sm" className="w-full justify-start text-sm h-8 px-2 font-normal">
                <FileText className="h-4 w-4 mr-2 text-muted-foreground" />
                Create new policy document
              </Button>
              <Button variant="ghost" size="sm" className="w-full justify-start text-sm h-8 px-2 font-normal">
                <User className="h-4 w-4 mr-2 text-muted-foreground" />
                Employee onboarding checklist
              </Button>
              <Button variant="ghost" size="sm" className="w-full justify-start text-sm h-8 px-2 font-normal">
                <ClipboardCheck className="h-4 w-4 mr-2 text-muted-foreground" />
                Generate compliance report
              </Button>
            </>
          ) : (
            <>
              <Button variant="ghost" size="sm" className="w-full justify-start text-sm h-8 px-2 font-normal">
                <FileText className="h-4 w-4 mr-2 text-muted-foreground" />
                Generate related documents
              </Button>
              <Button variant="ghost" size="sm" className="w-full justify-start text-sm h-8 px-2 font-normal">
                <AlertTriangle className="h-4 w-4 mr-2 text-muted-foreground" />
                Check compliance status
              </Button>
              <Button variant="ghost" size="sm" className="w-full justify-start text-sm h-8 px-2 font-normal">
                <User className="h-4 w-4 mr-2 text-muted-foreground" />
                Identify affected employees
              </Button>
            </>
          )}
        </div>
      </div>

      <div>
        <h4 className="text-xs font-medium text-muted-foreground mb-2">HR Insights</h4>
        <div className="bg-muted/50 p-3 rounded-md text-sm">
          {!activeItem ? (
            <p>
              Your compliance rate is currently at 98%. Two policies need review before the end of the month to maintain
              full compliance.
            </p>
          ) : (
            <p>
              Based on recent updates to labor laws, this document may need revision. I've identified 3 sections that
              might require updates.
            </p>
          )}
        </div>
      </div>
    </div>
  )
}


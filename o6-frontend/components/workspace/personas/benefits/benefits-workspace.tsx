"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { HeartPulse, DollarSign, Calendar, TrendingUp, TrendingDown, Building } from "lucide-react"
import type { WorkspaceItem } from "../../types"

interface BenefitsWorkspaceProps {
  activeItem: WorkspaceItem | null
  editMode: boolean
}

export function BenefitsWorkspace({ activeItem, editMode }: BenefitsWorkspaceProps) {
  // This is a simplified implementation

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center">
              <HeartPulse className="h-4 w-4 mr-2 text-primary" />
              Benefits Enrollment
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">78%</div>
            <Progress value={78} className="h-2 mt-2" />
            <p className="text-xs text-muted-foreground mt-1">192/247 employees enrolled</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center">
              <DollarSign className="h-4 w-4 mr-2 text-primary" />
              Benefits Utilization
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">$1.2M</div>
            <div className="flex items-center text-xs text-green-600 mt-1">
              <TrendingUp className="h-3 w-3 mr-1" />
              <span>12% increase from last year</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center">
              <Calendar className="h-4 w-4 mr-2 text-primary" />
              Open Enrollment
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">18</div>
            <p className="text-xs text-muted-foreground">days remaining</p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="overview">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="plans">Benefit Plans</TabsTrigger>
          <TabsTrigger value="enrollment">Enrollment</TabsTrigger>
          <TabsTrigger value="vendors">Vendors</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <h2 className="text-xl font-semibold mt-4">Benefits Summary</h2>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <BenefitCard
              title="Health Insurance"
              provider="Blue Cross Blue Shield"
              enrollmentRate={92}
              cost="$720/employee/month"
              trend="up"
            />

            <BenefitCard
              title="Dental Insurance"
              provider="Delta Dental"
              enrollmentRate={85}
              cost="$45/employee/month"
              trend="stable"
            />

            <BenefitCard
              title="Vision Insurance"
              provider="VSP"
              enrollmentRate={72}
              cost="$12/employee/month"
              trend="down"
            />

            <BenefitCard
              title="401(k) Plan"
              provider="Fidelity"
              enrollmentRate={68}
              cost="3% employer match"
              trend="up"
            />
          </div>

          <h2 className="text-xl font-semibold mt-6">Upcoming Events</h2>

          <div className="space-y-2">
            <EventCard
              title="Open Enrollment Webinar"
              date="June 15, 2023"
              description="Overview of benefit changes for the upcoming year"
            />

            <EventCard
              title="Wellness Program Launch"
              date="July 1, 2023"
              description="Introduction to the new employee wellness initiative"
            />
          </div>
        </TabsContent>

        <TabsContent value="plans">
          <p className="text-muted-foreground py-4">Benefit plans management content would go here</p>
        </TabsContent>

        <TabsContent value="enrollment">
          <p className="text-muted-foreground py-4">Enrollment campaign management content would go here</p>
        </TabsContent>

        <TabsContent value="vendors">
          <p className="text-muted-foreground py-4">Vendor management content would go here</p>
        </TabsContent>
      </Tabs>
    </div>
  )
}

interface BenefitCardProps {
  title: string
  provider: string
  enrollmentRate: number
  cost: string
  trend: "up" | "down" | "stable"
}

function BenefitCard({ title, provider, enrollmentRate, cost, trend }: BenefitCardProps) {
  return (
    <Card>
      <CardContent className="p-4">
        <div className="flex justify-between items-start">
          <div>
            <h3 className="font-medium">{title}</h3>
            <p className="text-sm text-muted-foreground flex items-center">
              <Building className="h-3 w-3 mr-1" />
              {provider}
            </p>
          </div>
          <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
            {enrollmentRate}% enrolled
          </Badge>
        </div>

        <div className="mt-3 flex justify-between items-center">
          <div className="text-sm">{cost}</div>
          <div className="flex items-center">
            {trend === "up" && <TrendingUp className="h-4 w-4 text-green-600" />}
            {trend === "down" && <TrendingDown className="h-4 w-4 text-red-600" />}
            {trend === "stable" && <span className="text-muted-foreground">—</span>}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

interface EventCardProps {
  title: string
  date: string
  description: string
}

function EventCard({ title, date, description }: EventCardProps) {
  return (
    <div className="flex items-start p-3 rounded-lg border">
      <div className="mr-3 mt-0.5">
        <Calendar className="h-4 w-4 text-primary" />
      </div>
      <div className="flex-1">
        <h3 className="font-medium">{title}</h3>
        <p className="text-sm font-medium">{date}</p>
        <p className="text-sm text-muted-foreground">{description}</p>
      </div>
    </div>
  )
}


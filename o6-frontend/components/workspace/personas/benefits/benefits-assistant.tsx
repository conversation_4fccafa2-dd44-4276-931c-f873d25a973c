"use client"

import { But<PERSON> } from "@/components/ui/button"
import { HeartPulse, DollarSign, Users, Calculator } from "lucide-react"
import type { WorkspaceItem } from "../../types"

interface BenefitsAssistantProps {
  activeItem: WorkspaceItem | null
}

export function BenefitsAssistant({ activeItem }: BenefitsAssistantProps) {
  return (
    <div className="p-4 space-y-4">
      <div className="flex items-start gap-2">
        <HeartPulse className="h-5 w-5 text-primary mt-0.5" />
        <p className="text-sm">
          {!activeItem && "Hello! I'm your Benefits Assistant. How can I help with your benefits administration today?"}
          {activeItem?.itemType === "benefits_plan" &&
            `I see you're reviewing the ${activeItem.title} plan. Would you like me to analyze utilization or suggest improvements?`}
          {activeItem?.itemType === "enrollment_campaign" &&
            `Looking at the ${activeItem.title} campaign. I can help with tracking progress or optimizing enrollment rates.`}
          {activeItem?.itemType === "vendor_contract" &&
            `Reviewing the ${activeItem.title} contract. Need help with terms analysis or renewal planning?`}
        </p>
      </div>

      <div>
        <h4 className="text-xs font-medium text-muted-foreground mb-2">Suggested Actions</h4>
        <div className="space-y-1">
          {!activeItem ? (
            <>
              <Button variant="ghost" size="sm" className="w-full justify-start text-sm h-8 px-2 font-normal">
                <HeartPulse className="h-4 w-4 mr-2 text-muted-foreground" />
                Create benefits plan comparison
              </Button>
              <Button variant="ghost" size="sm" className="w-full justify-start text-sm h-8 px-2 font-normal">
                <Users className="h-4 w-4 mr-2 text-muted-foreground" />
                Generate enrollment report
              </Button>
              <Button variant="ghost" size="sm" className="w-full justify-start text-sm h-8 px-2 font-normal">
                <DollarSign className="h-4 w-4 mr-2 text-muted-foreground" />
                Analyze benefits costs
              </Button>
            </>
          ) : (
            <>
              <Button variant="ghost" size="sm" className="w-full justify-start text-sm h-8 px-2 font-normal">
                <Calculator className="h-4 w-4 mr-2 text-muted-foreground" />
                Calculate cost projections
              </Button>
              <Button variant="ghost" size="sm" className="w-full justify-start text-sm h-8 px-2 font-normal">
                <Users className="h-4 w-4 mr-2 text-muted-foreground" />
                Identify eligible employees
              </Button>
              <Button variant="ghost" size="sm" className="w-full justify-start text-sm h-8 px-2 font-normal">
                <HeartPulse className="h-4 w-4 mr-2 text-muted-foreground" />
                Compare with industry benchmarks
              </Button>
            </>
          )}
        </div>
      </div>

      <div>
        <h4 className="text-xs font-medium text-muted-foreground mb-2">Benefits Insights</h4>
        <div className="bg-muted/50 p-3 rounded-md text-sm">
          {!activeItem ? (
            <p>
              Your current benefits enrollment rate is 78%, which is 5% higher than industry average. Healthcare plan
              utilization has increased by 12% compared to last year.
            </p>
          ) : (
            <p>
              Based on current enrollment trends, this plan is projected to reach 85% participation by the end of the
              enrollment period, exceeding your target of 80%.
            </p>
          )}
        </div>
      </div>
    </div>
  )
}


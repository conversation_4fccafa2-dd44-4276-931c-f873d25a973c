"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { GraduationCap, BookOpen, Award, Users, Calendar } from "lucide-react"
import type { WorkspaceItem } from "../../types"

interface LearningWorkspaceProps {
  activeItem: WorkspaceItem | null
  editMode: boolean
}

export function LearningWorkspace({ activeItem, editMode }: LearningWorkspaceProps) {
  // This is a simplified implementation

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center">
              <GraduationCap className="h-4 w-4 mr-2 text-primary" />
              Training Completion
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">83%</div>
            <Progress value={83} className="h-2 mt-2" />
            <p className="text-xs text-muted-foreground mt-1">205/247 employees completed</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center">
              <BookOpen className="h-4 w-4 mr-2 text-primary" />
              Active Courses
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">24</div>
            <p className="text-xs text-muted-foreground">8 new this quarter</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center">
              <Award className="h-4 w-4 mr-2 text-primary" />
              Certifications
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">156</div>
            <p className="text-xs text-muted-foreground">earned this year</p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="overview">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="programs">Programs</TabsTrigger>
          <TabsTrigger value="skills">Skills Matrix</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <h2 className="text-xl font-semibold mt-4">Popular Courses</h2>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <CourseCard
              title="Leadership Fundamentals"
              enrollments={42}
              completionRate={78}
              duration="4 weeks"
              format="Online"
            />

            <CourseCard
              title="Technical Writing"
              enrollments={36}
              completionRate={92}
              duration="2 weeks"
              format="Self-paced"
            />

            <CourseCard
              title="Project Management Essentials"
              enrollments={28}
              completionRate={65}
              duration="6 weeks"
              format="Hybrid"
            />

            <CourseCard
              title="Data Analysis with Python"
              enrollments={24}
              completionRate={70}
              duration="8 weeks"
              format="Online"
            />
          </div>

          <h2 className="text-xl font-semibold mt-6">Upcoming Training Sessions</h2>

          <div className="space-y-2">
            <SessionCard
              title="Customer Service Excellence"
              date="June 20, 2023"
              instructor="Maria Rodriguez"
              participants={15}
              maxParticipants={20}
            />

            <SessionCard
              title="Diversity and Inclusion Workshop"
              date="July 5, 2023"
              instructor="David Chen"
              participants={18}
              maxParticipants={25}
            />
          </div>
        </TabsContent>

        <TabsContent value="programs">
          <p className="text-muted-foreground py-4">Training programs management content would go here</p>
        </TabsContent>

        <TabsContent value="skills">
          <p className="text-muted-foreground py-4">Skills matrix content would go here</p>
        </TabsContent>

        <TabsContent value="analytics">
          <p className="text-muted-foreground py-4">Learning analytics content would go here</p>
        </TabsContent>
      </Tabs>
    </div>
  )
}

interface CourseCardProps {
  title: string
  enrollments: number
  completionRate: number
  duration: string
  format: string
}

function CourseCard({ title, enrollments, completionRate, duration, format }: CourseCardProps) {
  return (
    <Card>
      <CardContent className="p-4">
        <div className="flex justify-between items-start">
          <div>
            <h3 className="font-medium">{title}</h3>
            <p className="text-sm text-muted-foreground flex items-center">
              <Users className="h-3 w-3 mr-1" />
              {enrollments} enrolled
            </p>
          </div>
          <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
            {completionRate}% completion
          </Badge>
        </div>

        <div className="mt-3 flex justify-between items-center text-sm">
          <div>{duration}</div>
          <div>{format}</div>
        </div>
      </CardContent>
    </Card>
  )
}

interface SessionCardProps {
  title: string
  date: string
  instructor: string
  participants: number
  maxParticipants: number
}

function SessionCard({ title, date, instructor, participants, maxParticipants }: SessionCardProps) {
  return (
    <div className="flex items-start p-3 rounded-lg border">
      <div className="mr-3 mt-0.5">
        <Calendar className="h-4 w-4 text-primary" />
      </div>
      <div className="flex-1">
        <h3 className="font-medium">{title}</h3>
        <p className="text-sm font-medium">{date}</p>
        <p className="text-sm text-muted-foreground">Instructor: {instructor}</p>
      </div>
      <div className="text-right">
        <div className="text-sm font-medium">
          {participants}/{maxParticipants}
        </div>
        <p className="text-xs text-muted-foreground">participants</p>
      </div>
    </div>
  )
}


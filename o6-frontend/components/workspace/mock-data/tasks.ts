import type { TaskType, WorkspacePersona } from "../types"

// Add new recruiting tasks at the top of the file
const recruitingTasks: TaskType[] = [
  // Tasks for Senior Backend Engineers Hiring (obj-r1-1)
  {
    id: "task-r1-1",
    objectiveId: "obj-r1-1",
    title: "Create job descriptions for backend roles",
    description: "Develop comprehensive job descriptions for senior backend engineer positions specializing in distributed systems",
    dueDate: new Date(2025, 0, 10), // Jan 10
    status: "completed",
    priority: "high",
    assignedTo: "<PERSON>",
    relatedItems: ["Backend Engineer JD", "Distributed Systems Guide"],
    persona: "recruiter",
    type: "job_description"
  },
  {
    id: "task-r1-2",
    objectiveId: "obj-r1-1",
    title: "Post on specialized tech job boards",
    description: "Publish job listings on Stack Overflow, GitHub Jobs, and other tech-focused job boards",
    dueDate: new Date(2025, 0, 15), // Jan 15
    status: "completed",
    priority: "medium",
    assignedTo: "<PERSON>",
    relatedItems: ["Job Board Accounts", "Tech Recruitment Guide"],
    persona: "recruiter",
    type: "posting",
    noteId: "posting-note-1"
  },
  {
    id: "task-r1-3",
    objectiveId: "obj-r1-1",
    title: "Conduct technical screening interviews",
    description: "Schedule and conduct initial technical screenings for backend candidates",
    dueDate: new Date(2025, 0, 25), // Jan 25
    status: "in_progress",
    priority: "high",
    assignedTo: "Sarah Johnson",
    relatedItems: ["Technical Screening Template", "Interview Schedule"],
    persona: "recruiter",
    type: "screening",
    noteId: "screening-note-1"
  },
  {
    id: "task-r1-4",
    objectiveId: "obj-r1-1",
    title: "Coordinate with engineering leaders for interviews",
    description: "Schedule technical interviews with engineering team leaders for qualified candidates",
    dueDate: new Date(2025, 1, 5), // Feb 5
    status: "pending",
    priority: "medium",
    assignedTo: "Sarah Johnson",
    relatedItems: ["Engineering Leadership Calendar", "Interview Panel Guide"],
    persona: "recruiter",
    type: "meeting",
    noteId: "meeting-note-1"
  },

  // Tasks for Frontend Team Expansion (obj-r1-2)
  {
    id: "task-r1-5",
    objectiveId: "obj-r1-2",
    title: "Update frontend job requirements",
    description: "Review and update job requirements for frontend developers with React/NextJS focus",
    dueDate: new Date(2025, 0, 15), // Jan 15
    status: "completed",
    priority: "high",
    assignedTo: "David Wilson",
    relatedItems: ["Frontend Skills Assessment", "React Interview Questions"],
    persona: "recruiter",
    type: "job_description"
  },
  {
    id: "task-r1-6",
    objectiveId: "obj-r1-2",
    title: "Reach out to frontend developer communities",
    description: "Contact React and NextJS community leaders and groups to source candidates",
    dueDate: new Date(2025, 0, 20), // Jan 20
    status: "in_progress",
    priority: "medium",
    assignedTo: "Emily Rodriguez",
    relatedItems: ["Community Outreach Template", "Frontend Dev Groups"],
    persona: "recruiter",
    type: "sourcing",
    noteId: "sourcing-note-1"
  },

  // Tasks for Product Managers Hiring (obj-r2-1)
  {
    id: "task-r2-1",
    objectiveId: "obj-r2-1",
    title: "Define product manager role requirements",
    description: "Develop comprehensive job descriptions for senior product manager positions",
    dueDate: new Date(2025, 0, 10), // Jan 10
    status: "completed",
    priority: "medium",
    assignedTo: "David Wilson",
    relatedItems: ["Product Manager JD Template", "Product Skills Matrix"],
    persona: "recruiter",
    type: "job_description"
  },
  {
    id: "task-r2-2",
    objectiveId: "obj-r2-1",
    title: "Contact product management networks",
    description: "Reach out to product management communities and networks for referrals",
    dueDate: new Date(2025, 0, 15), // Jan 15
    status: "in_progress",
    priority: "medium",
    assignedTo: "Emily Rodriguez",
    relatedItems: ["Compensation Survey", "Market Analysis Report"],
    persona: "recruiter",
    type: "sourcing"
  }
];

export const tasks: TaskType[] = [
  // Include the new recruiting tasks first
  ...recruitingTasks,
  // Existing tasks
  {
    id: "task-1",
    objectiveId: "obj-1",
    title: "Create job descriptions for engineering roles",
    description: "Draft comprehensive job descriptions for senior and mid-level engineering positions",
    dueDate: new Date(2023, 0, 15),
    status: "completed",
    priority: "high",
    assignedTo: "Sarah Johnson",
    relatedItems: ["item-1", "item-2"],
    persona: "recruiter",
    type: "meeting",
  },
  {
    id: "task-2",
    objectiveId: "obj-1",
    title: "Post job openings on career sites",
    description: "Publish approved job descriptions on LinkedIn, Indeed, and company website",
    dueDate: new Date(2023, 0, 20),
    status: "completed",
    priority: "medium",
    assignedTo: "Sarah Johnson",
    relatedItems: ["item-3"],
    persona: "recruiter",
    type: "posting",
  },
  {
    id: "task-3",
    objectiveId: "obj-1",
    title: "Develop sourcing strategy for hard-to-fill roles",
    description: "Create targeted sourcing plans for specialized engineering positions",
    dueDate: new Date(2023, 0, 25),
    status: "in_progress",
    priority: "high",
    assignedTo: "Alex Rivera",
    relatedItems: [],
    persona: "recruiter",
    type: "sourcing",
  },
  {
    id: "task-4",
    objectiveId: "obj-2",
    title: "Screen initial candidates for engineering roles",
    description: "Conduct preliminary screening calls with potential candidates",
    dueDate: new Date(2023, 1, 5),
    status: "pending",
    priority: "medium",
    assignedTo: "Alex Rivera",
    relatedItems: ["item-4"],
    persona: "recruiter",
    type: "screening",
  },
  // Additional tasks...
  // Website Redesign (Objective 1) Tasks
  {
    id: "task-5",
    objectiveId: "obj-1",
    title: "Create wireframes",
    description: "Design wireframes for all key pages of the website",
    dueDate: new Date(2023, 0, 15), // Jan 15
    status: "completed",
    priority: "high",
    assignedTo: "Alex Rivera",
    relatedItems: [],
    persona: "hr-generalist",
  },
  {
    id: "task-6",
    objectiveId: "obj-1",
    title: "Develop homepage",
    description: "Code the new homepage based on approved designs",
    dueDate: new Date(2023, 0, 20), // Jan 20
    status: "completed",
    priority: "high",
    assignedTo: "Sarah Johnson",
    relatedItems: [],
    persona: "hr-generalist",
  },
  {
    id: "task-7",
    objectiveId: "obj-1",
    title: "Content migration",
    description: "Migrate content from old site to new site",
    dueDate: new Date(2023, 0, 25), // Jan 25
    status: "completed",
    priority: "medium",
    assignedTo: "John Doe",
    relatedItems: [],
    persona: "hr-generalist",
  },
  {
    id: "task-8",
    objectiveId: "obj-1",
    title: "QA Testing",
    description: "Perform quality assurance testing on all pages",
    dueDate: new Date(2023, 0, 28), // Jan 28
    status: "completed",
    priority: "high",
    assignedTo: "Lisa Park",
    relatedItems: [],
    persona: "hr-generalist",
  },

  // Content Strategy (Objective 2) Tasks
  {
    id: "task-9",
    objectiveId: "obj-2",
    title: "Audience research",
    description: "Conduct research to identify target audience segments",
    dueDate: new Date(2023, 1, 10), // Feb 10
    status: "completed",
    priority: "high",
    assignedTo: "Emily Rodriguez",
    relatedItems: [],
    persona: "hr-generalist",
  },
  {
    id: "task-10",
    objectiveId: "obj-2",
    title: "Content audit",
    description: "Audit existing content and identify gaps",
    dueDate: new Date(2023, 1, 15), // Feb 15
    status: "completed",
    priority: "medium",
    assignedTo: "John Doe",
    relatedItems: [],
    persona: "hr-generalist",
  },
  {
    id: "task-11",
    objectiveId: "obj-2",
    title: "Editorial calendar",
    description: "Create Q1 editorial calendar for all channels",
    dueDate: new Date(2023, 1, 20), // Feb 20
    status: "in_progress",
    priority: "high",
    assignedTo: "Lisa Park",
    relatedItems: [],
    persona: "hr-generalist",
  },
  {
    id: "task-12",
    objectiveId: "obj-2",
    title: "Style guide update",
    description: "Update content style guide with new brand voice",
    dueDate: new Date(2023, 1, 25), // Feb 25
    status: "pending",
    priority: "low",
    assignedTo: "Alex Rivera",
    relatedItems: [],
    persona: "hr-generalist",
  },

  // Social Media Campaign (Objective 3) Tasks
  {
    id: "task-13",
    objectiveId: "obj-3",
    title: "Platform strategy",
    description: "Define strategy for each social media platform",
    dueDate: new Date(2023, 2, 5), // Mar 5
    status: "completed",
    priority: "high",
    assignedTo: "Sarah Johnson",
    relatedItems: [],
    persona: "hr-generalist",
  },
  {
    id: "task-14",
    objectiveId: "obj-3",
    title: "Content creation",
    description: "Create social media content for launch campaign",
    dueDate: new Date(2023, 2, 15), // Mar 15
    status: "in_progress",
    priority: "high",
    assignedTo: "Emily Rodriguez",
    relatedItems: [],
    persona: "hr-generalist",
  },
  {
    id: "task-15",
    objectiveId: "obj-3",
    title: "Influencer outreach",
    description: "Identify and contact potential influencers for campaign",
    dueDate: new Date(2023, 2, 20), // Mar 20
    status: "in_progress",
    priority: "medium",
    assignedTo: "Lisa Park",
    relatedItems: [],
    persona: "hr-generalist",
  },
  {
    id: "task-16",
    objectiveId: "obj-3",
    title: "Analytics setup",
    description: "Set up tracking and analytics for campaign measurement",
    dueDate: new Date(2023, 2, 25), // Mar 25
    status: "pending",
    priority: "medium",
    assignedTo: "John Doe",
    relatedItems: [],
    persona: "hr-generalist",
  },

  // Engineering Team Expansion (Objective 4) Tasks
  {
    id: "task-17",
    objectiveId: "obj-4",
    title: "Job descriptions",
    description: "Create job descriptions for engineering roles",
    dueDate: new Date(2023, 0, 12), // Jan 12
    status: "completed",
    priority: "high",
    assignedTo: "Sarah Johnson",
    relatedItems: [],
    persona: "recruiter",
  },
  {
    id: "task-18",
    objectiveId: "obj-4",
    title: "Post job listings",
    description: "Post job listings on career sites and job boards",
    dueDate: new Date(2023, 0, 15), // Jan 15
    status: "completed",
    priority: "high",
    assignedTo: "David Wilson",
    relatedItems: [],
    persona: "recruiter",
  },
  {
    id: "task-19",
    objectiveId: "obj-4",
    title: "Resume screening",
    description: "Screen resumes for engineering candidates",
    dueDate: new Date(2023, 0, 25), // Jan 25
    status: "completed",
    priority: "medium",
    assignedTo: "Michael Chen",
    relatedItems: [],
    persona: "recruiter",
  },

  // Leadership Assessment (Objective 9) Tasks
  {
    id: "task-20",
    objectiveId: "obj-9",
    title: "Assessment tool selection",
    description: "Research and select leadership assessment tools",
    dueDate: new Date(2023, 0, 12), // Jan 12
    status: "completed",
    priority: "high",
    assignedTo: "Emily Rodriguez",
    relatedItems: [],
    persona: "learning",
  },
  {
    id: "task-21",
    objectiveId: "obj-9",
    title: "Manager communication",
    description: "Communicate assessment process to all managers",
    dueDate: new Date(2023, 0, 15), // Jan 15
    status: "completed",
    priority: "medium",
    assignedTo: "John Doe",
    relatedItems: [],
    persona: "learning",
  },
  {
    id: "task-22",
    objectiveId: "obj-9",
    title: "Assessment administration",
    description: "Administer leadership assessments to all managers",
    dueDate: new Date(2023, 0, 22), // Jan 22
    status: "completed",
    priority: "high",
    assignedTo: "Alex Rivera",
    relatedItems: [],
    persona: "learning",
  },

  // Wellness Assessment (Objective 7) Tasks
  {
    id: "task-23",
    objectiveId: "obj-7",
    title: "Survey design",
    description: "Design employee wellness survey",
    dueDate: new Date(2023, 3, 10), // Apr 10
    status: "pending",
    priority: "high",
    assignedTo: "Emily Rodriguez",
    relatedItems: [],
    persona: "benefits",
  },
  {
    id: "task-24",
    objectiveId: "obj-7",
    title: "Survey distribution",
    description: "Distribute wellness survey to all employees",
    dueDate: new Date(2023, 3, 15), // Apr 15
    status: "pending",
    priority: "medium",
    assignedTo: "Lisa Park",
    relatedItems: [],
    persona: "benefits",
  },
  {
    id: "task-25",
    title: "Client Meeting - Acme Financial",
    description: "Initial requirements gathering meeting with Acme Financial for Senior Python Developer role",
    dueDate: new Date(2023, 2, 5),
    status: "completed",
    priority: "high",
    assignedTo: "Sarah Johnson",
    objectiveId: "objective-1",
    type: "meeting",
    noteId: "meeting-note-1",
    relatedItems: [],
    persona: "recruiter",
  },
  {
    id: "task-26",
    title: "Create Job Description",
    description: "Create job descriptions for engineering roles",
    dueDate: new Date(2023, 0, 12),
    status: "completed",
    priority: "high",
    assignedTo: "Sarah Johnson",
    objectiveId: "objective-1",
    type: "posting",
    noteId: "posting-note-1",
    relatedItems: [],
    persona: "recruiter",
  },
  {
    id: "task-27",
    title: "Source Candidates",
    description: "Create boolean search strings and begin sourcing candidates",
    dueDate: new Date(2023, 0, 15),
    status: "completed",
    priority: "high",
    assignedTo: "David Wilson",
    objectiveId: "objective-1",
    type: "sourcing",
    noteId: "sourcing-note-1",
    relatedItems: [],
    persona: "recruiter",
  },
  {
    id: "task-28",
    title: "Screen Resumes",
    description: "Screen resumes for engineering candidates",
    dueDate: new Date(2023, 0, 25),
    status: "completed",
    priority: "medium",
    assignedTo: "Michael Chen",
    objectiveId: "objective-1",
    type: "screening",
    noteId: "screening-note-1",
    relatedItems: [],
    persona: "recruiter",
  },
]

export function getTaskById(id: string): TaskType | undefined {
  return tasks.find((task) => task.id === id)
}

export function getTasksByObjectiveId(objectiveId: string): TaskType[] {
  return tasks.filter((task) => task.objectiveId === objectiveId)
}

export function getMockTasks(objectiveId?: string, persona?: WorkspacePersona): TaskType[] {
  let filteredTasks = tasks;

  // Filter by objective ID if provided
  if (objectiveId) {
    filteredTasks = filteredTasks.filter((task) => task.objectiveId === objectiveId);
  }

  // Filter by persona if provided
  if (persona) {
    filteredTasks = filteredTasks.filter((task) => task.persona === persona);
  }

  return filteredTasks;
}

// Add a function for client-specific tasks
export function getClientMockTasks(objectiveId?: string): TaskType[] {
  const clientTasks: TaskType[] = [
    // Tasks for "Expand Tech Staffing Services" (client-obj-1-1)
    {
      id: "client-task-1-1",
      objectiveId: "client-obj-1-1",
      title: "Conduct Staffing Needs Analysis",
      description: "Review ABC Corp's current and future tech staffing requirements",
      dueDate: new Date(2025, 0, 15), // Jan 15, 2025
      status: "completed",
      priority: "high",
      assignedTo: "Sarah Johnson",
      relatedItems: ["ABC Corp Contract", "Tech Skills Matrix"],
      persona: "recruiter",
      type: "meeting"
    },
    {
      id: "client-task-1-2",
      objectiveId: "client-obj-1-1",
      title: "Develop Expansion Proposal",
      description: "Create proposal for expanding tech staffing services by 30%",
      dueDate: new Date(2025, 0, 25), // Jan 25, 2025
      status: "in_progress",
      priority: "high",
      assignedTo: "David Wilson",
      relatedItems: ["Proposal Template", "Rate Sheet"],
      persona: "recruiter",
      type: "posting"
    },
    
    // Tasks for "Develop Managed Services Agreement" (client-obj-1-2)
    {
      id: "client-task-2-1",
      objectiveId: "client-obj-1-2",
      title: "Draft MSA Terms",
      description: "Prepare initial MSA terms based on client requirements",
      dueDate: new Date(2025, 0, 20), // Jan 20, 2025
      status: "completed",
      priority: "medium",
      assignedTo: "Emily Rodriguez",
      relatedItems: ["MSA Template", "Legal Guidelines"],
      persona: "recruiter",
      type: "meeting"
    },
    {
      id: "client-task-2-2",
      objectiveId: "client-obj-1-2",
      title: "Legal Review of MSA",
      description: "Submit MSA for legal review and incorporate feedback",
      dueDate: new Date(2025, 0, 30), // Jan 30, 2025
      status: "in_progress",
      priority: "high",
      assignedTo: "David Wilson",
      relatedItems: ["Legal Review Process", "Compliance Requirements"],
      persona: "recruiter",
      type: "meeting"
    },
    
    // Tasks for "Initial Needs Assessment" (client-obj-2-1)
    {
      id: "client-task-3-1",
      objectiveId: "client-obj-2-1",
      title: "Schedule Client Interviews",
      description: "Arrange meetings with XYZ Inc. department heads",
      dueDate: new Date(2025, 0, 15), // Jan 15, 2025
      status: "completed",
      priority: "medium",
      assignedTo: "Sarah Johnson",
      relatedItems: ["XYZ Inc Contacts", "Meeting Schedule Template"],
      persona: "recruiter",
      type: "meeting"
    },
    {
      id: "client-task-3-2",
      objectiveId: "client-obj-2-1",
      title: "Compile Assessment Report",
      description: "Document findings from client interviews and present recommendations",
      dueDate: new Date(2025, 0, 25), // Jan 25, 2025
      status: "in_progress",
      priority: "high",
      assignedTo: "Emily Rodriguez",
      relatedItems: ["Assessment Template", "XYZ Inc Requirements"],
      persona: "recruiter",
      type: "posting"
    },
  ];

  if (objectiveId) {
    return clientTasks.filter(task => task.objectiveId === objectiveId);
  }
  
  return clientTasks;
}


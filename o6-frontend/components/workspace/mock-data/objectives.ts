import type { Objective, WorkspacePersona } from "../types"

export function getMockObjectives(programId?: string, persona?: WorkspacePersona): Objective[] {
  const now = new Date()
  const currentYear = now.getFullYear()

  const objectives: Objective[] = [
    // Program 1 Objectives (Q1 Marketing Campaign)
    {
      id: "obj-1",
      programId: "prog-1",
      title: "Website Redesign",
      description: "Complete redesign of the company website to support new product launch",
      month: 0, // January
      year: currentYear,
      status: "completed",
      progress: 100,
      owner: "<PERSON>",
      createdAt: new Date(currentYear, 0, 5), // Jan 5
      updatedAt: new Date(currentYear, 0, 28), // Jan 28
      persona: "hr-generalist",
      startDate: new Date(currentYear, 0, 5), // Jan 5
    },
    {
      id: "obj-2",
      programId: "prog-1",
      title: "Content Strategy",
      description: "Develop comprehensive content strategy for all marketing channels",
      month: 1, // February
      year: currentYear,
      status: "in_progress",
      progress: 75,
      owner: "Lisa Park",
      createdAt: new Date(currentYear, 1, 3), // Feb 3
      updatedAt: new Date(currentYear, 1, 20), // Feb 20
      persona: "hr-generalist",
      startDate: new Date(currentYear, 1, 3), // Feb 3
    },
    {
      id: "obj-3",
      programId: "prog-1",
      title: "Social Media Campaign",
      description: "Launch coordinated social media campaign across all platforms",
      month: 2, // March
      year: currentYear,
      status: "in_progress",
      progress: 40,
      owner: "Alex Rivera",
      createdAt: new Date(currentYear, 2, 1), // Mar 1
      updatedAt: new Date(currentYear, 2, 15), // Mar 15
      persona: "hr-generalist",
      startDate: new Date(currentYear, 2, 1), // Mar 1
    },

    // Engineering Talent Acquisition Program Objectives (prog-r1)
    {
      id: "obj-r1-1",
      programId: "prog-r1",
      title: "Senior Backend Engineers Hiring",
      description: "Recruit 3 senior backend engineers with distributed systems experience",
      month: 0, // January
      year: 2025,
      status: "in_progress",
      progress: 65,
      owner: "Sarah Johnson",
      createdAt: new Date(2025, 0, 5), // Jan 5
      updatedAt: new Date(2025, 0, 15), // Jan 15
      persona: "recruiter",
      startDate: new Date(2025, 0, 5), // Jan 5
    },
    {
      id: "obj-r1-2",
      programId: "prog-r1",
      title: "Frontend Team Expansion",
      description: "Hire 4 frontend developers with React/NextJS experience",
      month: 0, // January
      year: 2025,
      status: "in_progress",
      progress: 40,
      owner: "David Wilson",
      createdAt: new Date(2025, 0, 10), // Jan 10
      updatedAt: new Date(2025, 0, 20), // Jan 20
      persona: "recruiter",
      startDate: new Date(2025, 0, 10), // Jan 10
    },
    {
      id: "obj-r1-3",
      programId: "prog-r1",
      title: "DevOps Engineers Recruitment",
      description: "Build out DevOps team with 2 experienced engineers",
      month: 1, // February
      year: 2025,
      status: "not_started",
      progress: 0,
      owner: "Sarah Johnson",
      createdAt: new Date(2025, 0, 25), // Jan 25
      updatedAt: new Date(2025, 0, 25), // Jan 25
      persona: "recruiter",
      startDate: new Date(2025, 0, 25), // Jan 25
    },

    // Product Team Expansion Program Objectives (prog-r2)
    {
      id: "obj-r2-1",
      programId: "prog-r2",
      title: "Product Managers Hiring",
      description: "Recruit 2 senior product managers for our core product lines",
      month: 0, // January
      year: 2025,
      status: "in_progress",
      progress: 50,
      owner: "David Wilson",
      createdAt: new Date(2025, 0, 5), // Jan 5
      updatedAt: new Date(2025, 0, 15), // Jan 15
      persona: "recruiter",
      startDate: new Date(2025, 0, 5), // Jan 5
    },
    {
      id: "obj-r2-2",
      programId: "prog-r2",
      title: "UX/UI Designers Recruitment",
      description: "Hire 3 designers to strengthen our design capabilities",
      month: 0, // January
      year: 2025,
      status: "not_started",
      progress: 10,
      owner: "Emily Rodriguez",
      createdAt: new Date(2025, 0, 10), // Jan 10
      updatedAt: new Date(2025, 0, 15), // Jan 15
      persona: "recruiter",
      startDate: new Date(2025, 0, 10), // Jan 10
    },

    // Sales Team Development Program Objectives (prog-r3)
    {
      id: "obj-r3-1",
      programId: "prog-r3",
      title: "Regional Sales Representatives",
      description: "Recruit sales representatives for key regional markets",
      month: 0, // January
      year: 2025,
      status: "not_started",
      progress: 5,
      owner: "Emily Rodriguez",
      createdAt: new Date(2025, 0, 5), // Jan 5
      updatedAt: new Date(2025, 0, 10), // Jan 10
      persona: "recruiter",
      startDate: new Date(2025, 0, 5), // Jan 5
    },

    // Program 3 Objectives (Q1 Talent Acquisition)
    {
      id: "obj-4",
      programId: "prog-3",
      title: "Engineering Team Expansion",
      description: "Hire 5 senior engineers across frontend, backend, and DevOps",
      month: 0, // January
      year: currentYear,
      status: "completed",
      progress: 100,
      owner: "Sarah Johnson",
      createdAt: new Date(currentYear, 0, 10), // Jan 10
      updatedAt: new Date(currentYear, 0, 31), // Jan 31
      persona: "recruiter",
      startDate: new Date(currentYear, 0, 10), // Jan 10
    },
    {
      id: "obj-5",
      programId: "prog-3",
      title: "Product Team Growth",
      description: "Expand product team with 3 product managers and 2 designers",
      month: 1, // February
      year: currentYear,
      status: "completed",
      progress: 100,
      owner: "Michael Chen",
      createdAt: new Date(currentYear, 1, 5), // Feb 5
      updatedAt: new Date(currentYear, 1, 28), // Feb 28
      persona: "recruiter",
      startDate: new Date(currentYear, 1, 5), // Feb 5
    },
    {
      id: "obj-6",
      programId: "prog-3",
      title: "Internship Program",
      description: "Launch summer internship program for engineering and design",
      month: 2, // March
      year: currentYear,
      status: "in_progress",
      progress: 60,
      owner: "David Wilson",
      createdAt: new Date(currentYear, 2, 1), // Mar 1
      updatedAt: new Date(currentYear, 2, 20), // Mar 20
      persona: "recruiter",
      startDate: new Date(currentYear, 2, 1), // Mar 1
    },

    // Program 4 Objectives (Q2 Employee Wellness)
    {
      id: "obj-7",
      programId: "prog-4",
      title: "Wellness Assessment",
      description: "Conduct company-wide wellness assessment and needs analysis",
      month: 3, // April
      year: currentYear,
      status: "not_started",
      progress: 0,
      owner: "Emily Rodriguez",
      createdAt: new Date(currentYear, 2, 15), // Mar 15
      updatedAt: new Date(currentYear, 2, 15), // Mar 15
      persona: "benefits",
      startDate: new Date(currentYear, 2, 15), // Mar 15
    },
    {
      id: "obj-8",
      programId: "prog-4",
      title: "Mental Health Resources",
      description: "Expand mental health resources and support services",
      month: 4, // May
      year: currentYear,
      status: "not_started",
      progress: 0,
      owner: "Lisa Park",
      createdAt: new Date(currentYear, 2, 20), // Mar 20
      updatedAt: new Date(currentYear, 2, 20), // Mar 20
      persona: "benefits",
      startDate: new Date(currentYear, 2, 20), // Mar 20
    },

    // Program 5 Objectives (Q1 Leadership Development)
    {
      id: "obj-9",
      programId: "prog-5",
      title: "Leadership Assessment",
      description: "Conduct leadership skills assessment for all managers",
      month: 0, // January
      year: currentYear,
      status: "completed",
      progress: 100,
      owner: "John Doe",
      createdAt: new Date(currentYear, 0, 8), // Jan 8
      updatedAt: new Date(currentYear, 0, 25), // Jan 25
      persona: "learning",
      startDate: new Date(currentYear, 0, 8), // Jan 8
    },
    {
      id: "obj-10",
      programId: "prog-5",
      title: "Training Curriculum Development",
      description: "Develop comprehensive leadership training curriculum",
      month: 1, // February
      year: currentYear,
      status: "completed",
      progress: 100,
      owner: "Alex Rivera",
      createdAt: new Date(currentYear, 1, 1), // Feb 1
      updatedAt: new Date(currentYear, 1, 28), // Feb 28
      persona: "learning",
      startDate: new Date(currentYear, 1, 1), // Feb 1
    },
    {
      id: "obj-11",
      programId: "prog-5",
      title: "Leadership Workshop Series",
      description: "Conduct series of leadership workshops for all managers",
      month: 2, // March
      year: currentYear,
      status: "completed",
      progress: 100,
      owner: "Emily Rodriguez",
      createdAt: new Date(currentYear, 2, 1), // Mar 1
      updatedAt: new Date(currentYear, 2, 25), // Mar 25
      persona: "learning",
      startDate: new Date(currentYear, 2, 1), // Mar 1
    },
  ]

  // Filter by program ID if provided
  if (programId) {
    return objectives.filter((objective) => objective.programId === programId)
  }

  // Filter by persona if provided
  if (persona) {
    return objectives.filter((objective) => objective.persona === persona)
  }

  return objectives
}

// Add a function for client-specific objectives
export function getClientMockObjectives(programId?: string): Objective[] {
  const objectives: Objective[] = [
    // Objectives for ABC Corp Account Expansion
    {
      id: "client-obj-1-1",
      programId: "client-prog-1",
      title: "Expand Tech Staffing Services",
      description: "Increase tech staffing services for ABC Corp by 30%",
      month: 0, // January
      year: 2025,
      status: "in_progress",
      progress: 60,
      owner: "Sarah Johnson",
      createdAt: new Date(2025, 0, 5),
      updatedAt: new Date(2025, 0, 15),
      persona: "recruiter",
      startDate: new Date(2025, 0, 5),
    },
    {
      id: "client-obj-1-2",
      programId: "client-prog-1",
      title: "Develop Managed Services Agreement",
      description: "Create and finalize MSA for expanded services",
      month: 0, // January
      year: 2025,
      status: "in_progress",
      progress: 70,
      owner: "David Wilson",
      createdAt: new Date(2025, 0, 10),
      updatedAt: new Date(2025, 0, 20),
      persona: "recruiter",
      startDate: new Date(2025, 0, 10),
    },
    
    // Objectives for XYZ Inc. New Contract
    {
      id: "client-obj-2-1",
      programId: "client-prog-2",
      title: "Initial Needs Assessment",
      description: "Complete full staffing needs assessment for XYZ Inc.",
      month: 0, // January
      year: 2025,
      status: "in_progress",
      progress: 40,
      owner: "Emily Rodriguez",
      createdAt: new Date(2025, 0, 10),
      updatedAt: new Date(2025, 0, 15),
      persona: "recruiter",
      startDate: new Date(2025, 0, 10),
    },
    {
      id: "client-obj-2-2",
      programId: "client-prog-2",
      title: "Contract Negotiation",
      description: "Negotiate contract terms and pricing structure",
      month: 1, // February
      year: 2025,
      status: "not_started",
      progress: 0,
      owner: "Sarah Johnson",
      createdAt: new Date(2025, 0, 15),
      updatedAt: new Date(2025, 0, 15),
      persona: "recruiter",
      startDate: new Date(2025, 0, 15),
    },
    
    // Objectives for Tech Innovators Partnership
    {
      id: "client-obj-3-1",
      programId: "client-prog-3",
      title: "Strategic Partnership Framework",
      description: "Develop framework for strategic staffing partnership",
      month: 0, // January
      year: 2025,
      status: "in_progress",
      progress: 50,
      owner: "David Wilson",
      createdAt: new Date(2025, 0, 15),
      updatedAt: new Date(2025, 0, 25),
      persona: "recruiter",
      startDate: new Date(2025, 0, 15),
    },
  ];
  
  if (programId) {
    return objectives.filter(obj => obj.programId === programId);
  }
  
  return objectives;
}


"use client"

import type React from "react"

import { But<PERSON> } from "@/components/ui/button"
import {
  ChevronRight,
  FileText,
  Filter,
  Users,
  MessageSquare,
  Clock,
  Star,
  Archive,
  BriefcaseBusiness,
  CalendarCheck,
} from "lucide-react"
import { cn } from "@/lib/utils"
import type { WorkspaceItem, TaskType } from "../types"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import { Separator } from "@/components/ui/separator"
import { formatTimeAgo } from "../utils"

interface SidebarAllProps {
  workspaceItems: WorkspaceItem[]
  filteredItems: WorkspaceItem[]
  tasks: TaskType[]
  activeItem: WorkspaceItem | null
  onItemSelect: (item: WorkspaceItem) => void
  onTabChange: (tab: string) => void
  searchQuery: string
}

export function SidebarAll({
  workspaceItems,
  filteredItems,
  tasks,
  activeItem,
  onItemSelect,
  onTabChange,
  searchQuery,
}: SidebarAllProps) {
  return (
    <div className="px-2">
      <div className="mb-4">
        <Button variant="ghost" className="w-full justify-start mb-1 font-medium text-muted-foreground" disabled>
          <CalendarCheck className="h-4 w-4 mr-2" />
          <span>Today's Tasks</span>
        </Button>

        {tasks
          .filter((task) => {
            const today = new Date()
            const taskDate = new Date(task.dueDate)
            return taskDate.toDateString() === today.toDateString()
          })
          .map((task) => (
            <Button
              key={task.id}
              variant="ghost"
              className="w-full justify-start mb-1 font-normal pl-9 text-left"
              onClick={() => {
                // Task handling logic
              }}
            >
              <div className="flex items-center space-x-2 w-full">
                <div
                  className={cn(
                    "h-2 w-2 rounded-full",
                    task.priority === "high"
                      ? "bg-destructive"
                      : task.priority === "medium"
                        ? "bg-amber-500"
                        : "bg-green-500",
                  )}
                />
                <span className="truncate">{task.title}</span>
              </div>
            </Button>
          ))}
      </div>

      <Separator className="my-2" />

      {/* Time-based organization */}
      <div className="mb-3">
        <Button variant="ghost" className="w-full justify-start mb-1 font-medium text-muted-foreground" disabled>
          <BriefcaseBusiness className="h-4 w-4 mr-2" />
          <span>Recent Items</span>
        </Button>

        {workspaceItems
          .sort((a, b) => b.updatedAt.getTime() - a.updatedAt.getTime())
          .slice(0, 5)
          .map((item) => (
            <Button
              key={item.id}
              variant="ghost"
              className={cn("w-full justify-start mb-1 font-normal pl-9", activeItem?.id === item.id && "bg-accent")}
              onClick={() => onItemSelect(item)}
            >
              {item.type === "job_description" && <FileText className="h-4 w-4 mr-2" />}
              {item.type === "boolean_search" && <Filter className="h-4 w-4 mr-2" />}
              {item.type === "candidate_profile" && <Users className="h-4 w-4 mr-2" />}
              {item.type === "meeting_notes" && <MessageSquare className="h-4 w-4 mr-2" />}
              <span className="truncate">{item.title}</span>
            </Button>
          ))}
      </div>

      {/* Type-based organization */}
      <TypeSection
        title="Job Descriptions"
        icon={<FileText className="h-4 w-4 mr-2" />}
        items={filteredItems.filter((item) => item.type === "job_description")}
        activeItem={activeItem}
        onItemSelect={onItemSelect}
        onViewAll={() => onTabChange("category")}
      />

      <TypeSection
        title="Boolean Searches"
        icon={<Filter className="h-4 w-4 mr-2" />}
        items={filteredItems.filter((item) => item.type === "boolean_search")}
        activeItem={activeItem}
        onItemSelect={onItemSelect}
        onViewAll={() => onTabChange("category")}
      />

      <TypeSection
        title="Candidate Profiles"
        icon={<Users className="h-4 w-4 mr-2" />}
        items={filteredItems.filter((item) => item.type === "candidate_profile")}
        activeItem={activeItem}
        onItemSelect={onItemSelect}
        onViewAll={() => onTabChange("category")}
      />

      <TypeSection
        title="Meeting Notes"
        icon={<MessageSquare className="h-4 w-4 mr-2" />}
        items={filteredItems.filter((item) => item.type === "meeting_notes")}
        activeItem={activeItem}
        onItemSelect={onItemSelect}
        onViewAll={() => onTabChange("category")}
      />

      {/* Status-based organization */}
      <Collapsible className="mb-3">
        <CollapsibleTrigger asChild>
          <Button variant="ghost" className="w-full justify-start mb-1 font-medium">
            <ChevronRight className="h-4 w-4 mr-2 transition-transform duration-200 group-data-[state=open]:rotate-90" />
            <Clock className="h-4 w-4 mr-2" />
            <span>Recently Updated</span>
          </Button>
        </CollapsibleTrigger>
        <CollapsibleContent>
          {filteredItems
            .sort((a, b) => b.updatedAt.getTime() - a.updatedAt.getTime())
            .slice(0, 10)
            .map((item) => (
              <Button
                key={item.id}
                variant="ghost"
                className={cn("w-full justify-start mb-1 font-normal pl-9", activeItem?.id === item.id && "bg-accent")}
                onClick={() => onItemSelect(item)}
              >
                {item.type === "job_description" && <FileText className="h-4 w-4 mr-2" />}
                {item.type === "boolean_search" && <Filter className="h-4 w-4 mr-2" />}
                {item.type === "candidate_profile" && <Users className="h-4 w-4 mr-2" />}
                {item.type === "meeting_notes" && <MessageSquare className="h-4 w-4 mr-2" />}
                <span className="truncate">{item.title}</span>
                <span className="ml-auto text-xs text-muted-foreground">{formatTimeAgo(item.updatedAt)}</span>
              </Button>
            ))}
        </CollapsibleContent>
      </Collapsible>

      <Collapsible className="mb-3">
        <CollapsibleTrigger asChild>
          <Button variant="ghost" className="w-full justify-start mb-1 font-medium">
            <ChevronRight className="h-4 w-4 mr-2 transition-transform duration-200 group-data-[state=open]:rotate-90" />
            <Star className="h-4 w-4 mr-2" />
            <span>Favorites</span>
            <span className="ml-auto text-xs text-muted-foreground">
              {workspaceItems.filter((item) => item.status === "favorite").length || 0}
            </span>
          </Button>
        </CollapsibleTrigger>
        <CollapsibleContent>
          {filteredItems
            .filter((item) => item.status === "favorite")
            .map((item) => (
              <Button
                key={item.id}
                variant="ghost"
                className={cn("w-full justify-start mb-1 font-normal pl-9", activeItem?.id === item.id && "bg-accent")}
                onClick={() => onItemSelect(item)}
              >
                {item.type === "job_description" && <FileText className="h-4 w-4 mr-2" />}
                {item.type === "boolean_search" && <Filter className="h-4 w-4 mr-2" />}
                {item.type === "candidate_profile" && <Users className="h-4 w-4 mr-2" />}
                {item.type === "meeting_notes" && <MessageSquare className="h-4 w-4 mr-2" />}
                <span className="truncate">{item.title}</span>
              </Button>
            ))}
          {filteredItems.filter((item) => item.status === "favorite").length === 0 && (
            <div className="px-4 py-2 text-sm text-muted-foreground">
              No favorites yet. Click the star icon on any item to add it to favorites.
            </div>
          )}
        </CollapsibleContent>
      </Collapsible>

      <Collapsible className="mb-3">
        <CollapsibleTrigger asChild>
          <Button variant="ghost" className="w-full justify-start mb-1 font-medium">
            <ChevronRight className="h-4 w-4 mr-2 transition-transform duration-200 group-data-[state=open]:rotate-90" />
            <Archive className="h-4 w-4 mr-2" />
            <span>Archived</span>
            <span className="ml-auto text-xs text-muted-foreground">
              {workspaceItems.filter((item) => item.status === "archived").length}
            </span>
          </Button>
        </CollapsibleTrigger>
        <CollapsibleContent>
          {filteredItems
            .filter((item) => item.status === "archived")
            .slice(0, 10)
            .map((item) => (
              <Button
                key={item.id}
                variant="ghost"
                className={cn("w-full justify-start mb-1 font-normal pl-9", activeItem?.id === item.id && "bg-accent")}
                onClick={() => onItemSelect(item)}
              >
                {item.type === "job_description" && <FileText className="h-4 w-4 mr-2" />}
                {item.type === "boolean_search" && <Filter className="h-4 w-4 mr-2" />}
                {item.type === "candidate_profile" && <Users className="h-4 w-4 mr-2" />}
                {item.type === "meeting_notes" && <MessageSquare className="h-4 w-4 mr-2" />}
                <span className="truncate">{item.title}</span>
              </Button>
            ))}
        </CollapsibleContent>
      </Collapsible>
    </div>
  )
}

interface TypeSectionProps {
  title: string
  icon: React.ReactNode
  items: WorkspaceItem[]
  activeItem: WorkspaceItem | null
  onItemSelect: (item: WorkspaceItem) => void
  onViewAll: () => void
}

function TypeSection({ title, icon, items, activeItem, onItemSelect, onViewAll }: TypeSectionProps) {
  return (
    <Collapsible className="mb-3">
      <CollapsibleTrigger asChild>
        <Button variant="ghost" className="w-full justify-start mb-1 font-medium">
          <ChevronRight className="h-4 w-4 mr-2 transition-transform duration-200 group-data-[state=open]:rotate-90" />
          {icon}
          <span>{title}</span>
          <span className="ml-auto text-xs text-muted-foreground">{items.length}</span>
        </Button>
      </CollapsibleTrigger>
      <CollapsibleContent>
        {items.slice(0, 10).map((item) => (
          <Button
            key={item.id}
            variant="ghost"
            className={cn("w-full justify-start mb-1 font-normal pl-9", activeItem?.id === item.id && "bg-accent")}
            onClick={() => onItemSelect(item)}
          >
            {icon}
            <span className="truncate">{item.title}</span>
          </Button>
        ))}
        {items.length > 10 && (
          <Button
            variant="ghost"
            className="w-full justify-start mb-1 font-normal pl-9 text-primary"
            onClick={onViewAll}
          >
            <span className="truncate">View all ({items.length})</span>
          </Button>
        )}
      </CollapsibleContent>
    </Collapsible>
  )
}


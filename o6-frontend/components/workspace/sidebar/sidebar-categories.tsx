"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { ChevronRight, Folder, FileText, Filter, Users, MessageSquare } from "lucide-react"
import { cn } from "@/lib/utils"
import type { WorkspaceItem } from "../types"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"

interface SidebarCategoriesProps {
  categories: string[]
  workspaceItems: WorkspaceItem[]
  activeItem: WorkspaceItem | null
  onItemSelect: (item: WorkspaceItem) => void
  onTabChange: (tab: string) => void
}

export function SidebarCategories({
  categories,
  workspaceItems,
  activeItem,
  onItemSelect,
  onTabChange,
}: SidebarCategoriesProps) {
  return (
    <div className="px-2">
      {categories.map((category) => (
        <Collapsible key={category} className="mb-2">
          <CollapsibleTrigger asChild>
            <Button variant="ghost" className="w-full justify-start mb-1 font-medium">
              <ChevronRight className="h-4 w-4 mr-2 transition-transform duration-200 group-data-[state=open]:rotate-90" />
              <Folder className="h-4 w-4 mr-2" />
              <span>{category}</span>
              <span className="ml-auto text-xs text-muted-foreground">
                {workspaceItems.filter((item) => item.category === category).length}
              </span>
            </Button>
          </CollapsibleTrigger>
          <CollapsibleContent>
            {/* Group by type within each category */}
            {["job_description", "boolean_search", "candidate_profile", "meeting_notes"].map((type) => {
              const typeItems = workspaceItems.filter((item) => item.category === category && item.type === type)

              if (typeItems.length === 0) return null

              return (
                <div key={`${category}-${type}`} className="mb-2">
                  <div className="pl-9 py-1 text-xs font-medium text-muted-foreground">
                    {type === "job_description" && "Job Descriptions"}
                    {type === "boolean_search" && "Boolean Searches"}
                    {type === "candidate_profile" && "Candidate Profiles"}
                    {type === "meeting_notes" && "Meeting Notes"}
                  </div>
                  {typeItems.map((item) => (
                    <Button
                      key={item.id}
                      variant="ghost"
                      className={cn(
                        "w-full justify-start mb-1 font-normal pl-12",
                        activeItem?.id === item.id && "bg-accent",
                      )}
                      onClick={() => onItemSelect(item)}
                    >
                      {item.type === "job_description" && <FileText className="h-4 w-4 mr-2" />}
                      {item.type === "boolean_search" && <Filter className="h-4 w-4 mr-2" />}
                      {item.type === "candidate_profile" && <Users className="h-4 w-4 mr-2" />}
                      {item.type === "meeting_notes" && <MessageSquare className="h-4 w-4 mr-2" />}
                      <span className="truncate">{item.title}</span>
                    </Button>
                  ))}
                </div>
              )
            })}
          </CollapsibleContent>
        </Collapsible>
      ))}
    </div>
  )
}


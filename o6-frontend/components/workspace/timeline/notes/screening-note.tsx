"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Edit, Save, X } from "lucide-react"
import { Textarea } from "@/components/ui/textarea"
import { Input } from "@/components/ui/input"
import type { Note } from "../../types"
import { Progress } from "@/components/ui/progress"

interface ScreeningNoteTemplateProps {
  note: Note
  onSave?: (note: Note) => void
}

export function ScreeningNoteTemplate({ note, onSave }: ScreeningNoteTemplateProps) {
  const [isEditing, setIsEditing] = useState(false)
  const [editedNote, setEditedNote] = useState<Note>(note)

  const handleSave = () => {
    setIsEditing(false)
    if (onSave) {
      onSave(editedNote)
    }
  }

  const handleCancel = () => {
    setIsEditing(false)
    setEditedNote(note)
  }

  const updateSkill = (index: number, field: "skill" | "rating", value: any) => {
    const updatedSkills = [...editedNote.content.skillsAssessment]
    if (field === "rating") {
      value = Number.parseInt(value, 10)
      if (isNaN(value) || value < 1) value = 1
      if (value > 10) value = 10
    }
    updatedSkills[index] = { ...updatedSkills[index], [field]: value }
    setEditedNote({
      ...editedNote,
      content: {
        ...editedNote.content,
        skillsAssessment: updatedSkills,
      },
    })
  }

  return (
    <Card className="mt-6">
      <CardHeader className="pb-2 flex flex-row items-center justify-between">
        <CardTitle className="text-xl font-semibold">{note.content.title}</CardTitle>
        {isEditing ? (
          <div className="flex space-x-2">
            <Button variant="outline" size="sm" onClick={handleCancel}>
              <X className="h-4 w-4 mr-1" /> Cancel
            </Button>
            <Button size="sm" onClick={handleSave}>
              <Save className="h-4 w-4 mr-1" /> Save
            </Button>
          </div>
        ) : (
          <Button variant="outline" size="sm" onClick={() => setIsEditing(true)}>
            <Edit className="h-4 w-4 mr-1" /> Edit
          </Button>
        )}
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          <div>
            <h3 className="text-lg font-medium mb-2">Professional Summary</h3>
            {isEditing ? (
              <Textarea
                value={editedNote.content.professionalSummary}
                onChange={(e) =>
                  setEditedNote({
                    ...editedNote,
                    content: {
                      ...editedNote.content,
                      professionalSummary: e.target.value,
                    },
                  })
                }
                rows={3}
              />
            ) : (
              <p className="text-gray-700">{note.content.professionalSummary}</p>
            )}
          </div>

          <div>
            <h3 className="text-lg font-medium mb-2">Skills Assessment</h3>
            <div className="bg-gray-50 p-4 rounded-lg">
              {isEditing ? (
                <div className="space-y-3">
                  {editedNote.content.skillsAssessment.map((skill, index) => (
                    <div key={index} className="grid grid-cols-12 gap-2 items-center">
                      <div className="col-span-3">
                        <Input
                          value={skill.skill}
                          onChange={(e) => updateSkill(index, "skill", e.target.value)}
                          className="text-sm"
                        />
                      </div>
                      <div className="col-span-7">
                        <Input
                          type="range"
                          min="1"
                          max="10"
                          value={skill.rating}
                          onChange={(e) => updateSkill(index, "rating", e.target.value)}
                          className="w-full"
                        />
                      </div>
                      <div className="col-span-2 text-right">
                        <Input
                          type="number"
                          min="1"
                          max="10"
                          value={skill.rating}
                          onChange={(e) => updateSkill(index, "rating", e.target.value)}
                          className="w-16 text-center"
                        />
                      </div>
                    </div>
                  ))}
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() =>
                      setEditedNote({
                        ...editedNote,
                        content: {
                          ...editedNote.content,
                          skillsAssessment: [...editedNote.content.skillsAssessment, { skill: "", rating: 5 }],
                        },
                      })
                    }
                  >
                    Add Skill
                  </Button>
                </div>
              ) : (
                <div className="space-y-3">
                  {note.content.skillsAssessment.map((skill, index) => (
                    <div key={index} className="grid grid-cols-12 gap-2 items-center">
                      <div className="col-span-2 font-medium">{skill.skill}</div>
                      <div className="col-span-8">
                        <Progress value={skill.rating * 10} className="h-2" />
                      </div>
                      <div className="col-span-2 text-right">{skill.rating}/10</div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          <div>
            <h3 className="text-lg font-medium mb-2">Screening Notes</h3>
            {isEditing ? (
              <Textarea
                value={editedNote.content.screeningNotes}
                onChange={(e) =>
                  setEditedNote({
                    ...editedNote,
                    content: {
                      ...editedNote.content,
                      screeningNotes: e.target.value,
                    },
                  })
                }
                rows={4}
              />
            ) : (
              <p className="text-gray-700">{note.content.screeningNotes}</p>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}


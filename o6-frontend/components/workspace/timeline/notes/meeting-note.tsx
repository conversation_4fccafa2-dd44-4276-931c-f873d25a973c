"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Edit, Save, X } from "lucide-react"
import { Textarea } from "@/components/ui/textarea"
import { Input } from "@/components/ui/input"
import type { MeetingNote } from "../../types"

interface MeetingNoteProps {
  note: MeetingNote
  onSave?: (note: MeetingNote) => void
}

export function MeetingNoteTemplate({ note, onSave }: MeetingNoteProps) {
  const [isEditing, setIsEditing] = useState(false)
  const [editedNote, setEditedNote] = useState<MeetingNote>(note)

  const handleSave = () => {
    if (onSave) {
      onSave(editedNote)
    }
    setIsEditing(false)
  }

  const handleCancel = () => {
    setEditedNote(note)
    setIsEditing(false)
  }

  const updateRoleRequirement = (index: number, value: string) => {
    const newRequirements = [...editedNote.roleRequirements]
    newRequirements[index] = value
    setEditedNote({ ...editedNote, roleRequirements: newRequirements })
  }

  const updateNextStep = (index: number, value: string) => {
    const newSteps = [...editedNote.nextSteps]
    newSteps[index] = value
    setEditedNote({ ...editedNote, nextSteps: newSteps })
  }

  return (
    <Card className="mt-6">
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <CardTitle className="text-xl font-bold">
          {isEditing ? (
            <Input
              value={editedNote.title}
              onChange={(e) => setEditedNote({ ...editedNote, title: e.target.value })}
              className="font-bold text-xl"
            />
          ) : (
            note.title
          )}
        </CardTitle>
        <div>
          {isEditing ? (
            <div className="flex space-x-2">
              <Button size="sm" onClick={handleSave}>
                <Save className="h-4 w-4 mr-1" />
                Save
              </Button>
              <Button size="sm" variant="outline" onClick={handleCancel}>
                <X className="h-4 w-4 mr-1" />
                Cancel
              </Button>
            </div>
          ) : (
            <Button size="sm" variant="outline" onClick={() => setIsEditing(true)}>
              <Edit className="h-4 w-4 mr-1" />
              Edit
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        <div>
          <h3 className="text-lg font-semibold mb-2">Meeting Details</h3>
          {isEditing ? (
            <div className="space-y-2">
              <div>
                <label className="text-sm font-medium">Date</label>
                <Input
                  type="date"
                  value={new Date(editedNote.date).toISOString().split("T")[0]}
                  onChange={(e) => setEditedNote({ ...editedNote, date: new Date(e.target.value) })}
                  className="mt-1"
                />
              </div>
              <div>
                <label className="text-sm font-medium">Attendees</label>
                <Textarea
                  value={editedNote.attendees.join("\n")}
                  onChange={(e) => setEditedNote({ ...editedNote, attendees: e.target.value.split("\n") })}
                  className="mt-1"
                />
              </div>
            </div>
          ) : (
            <p>
              Date: {new Date(note.date).toLocaleDateString()} <br />
              Attendees: {note.attendees.join(", ")}
            </p>
          )}
        </div>

        <div>
          <h3 className="text-lg font-semibold mb-2">Role Requirements (Full Kit)</h3>
          {isEditing ? (
            <div className="space-y-2">
              {editedNote.roleRequirements.map((req, index) => (
                <div key={index} className="flex items-center">
                  <span className="mr-2">•</span>
                  <Input
                    value={req}
                    onChange={(e) => updateRoleRequirement(index, e.target.value)}
                    className="flex-1"
                  />
                </div>
              ))}
              <Button
                size="sm"
                variant="outline"
                onClick={() => setEditedNote({ ...editedNote, roleRequirements: [...editedNote.roleRequirements, ""] })}
              >
                Add Requirement
              </Button>
            </div>
          ) : (
            <ul className="list-disc pl-5 space-y-1">
              {note.roleRequirements.map((req, index) => (
                <li key={index}>{req}</li>
              ))}
            </ul>
          )}
        </div>

        <div>
          <h3 className="text-lg font-semibold mb-2">Compensation & Timeline</h3>
          {isEditing ? (
            <div className="space-y-2">
              <div>
                <label className="text-sm font-medium">Budget</label>
                <Input
                  value={editedNote.compensation.budget}
                  onChange={(e) =>
                    setEditedNote({
                      ...editedNote,
                      compensation: { ...editedNote.compensation, budget: e.target.value },
                    })
                  }
                  className="mt-1"
                />
              </div>
              <div>
                <label className="text-sm font-medium">Benefits</label>
                <Input
                  value={editedNote.compensation.benefits}
                  onChange={(e) =>
                    setEditedNote({
                      ...editedNote,
                      compensation: { ...editedNote.compensation, benefits: e.target.value },
                    })
                  }
                  className="mt-1"
                />
              </div>
              <div>
                <label className="text-sm font-medium">Start Date</label>
                <Input
                  value={editedNote.timeline.startDate}
                  onChange={(e) =>
                    setEditedNote({ ...editedNote, timeline: { ...editedNote.timeline, startDate: e.target.value } })
                  }
                  className="mt-1"
                />
              </div>
            </div>
          ) : (
            <div>
              <p>Budget: {note.compensation.budget}</p>
              <p>Benefits: {note.compensation.benefits}</p>
              <p>Start date: {note.timeline.startDate}</p>
            </div>
          )}
        </div>

        <div>
          <h3 className="text-lg font-semibold mb-2">Next Steps</h3>
          {isEditing ? (
            <div className="space-y-2">
              {editedNote.nextSteps.map((step, index) => (
                <div key={index} className="flex items-center">
                  <span className="mr-2">•</span>
                  <Input value={step} onChange={(e) => updateNextStep(index, e.target.value)} className="flex-1" />
                </div>
              ))}
              <Button
                size="sm"
                variant="outline"
                onClick={() => setEditedNote({ ...editedNote, nextSteps: [...editedNote.nextSteps, ""] })}
              >
                Add Step
              </Button>
            </div>
          ) : (
            <ul className="list-disc pl-5 space-y-1">
              {note.nextSteps.map((step, index) => (
                <li key={index}>{step}</li>
              ))}
            </ul>
          )}
        </div>
      </CardContent>
    </Card>
  )
}


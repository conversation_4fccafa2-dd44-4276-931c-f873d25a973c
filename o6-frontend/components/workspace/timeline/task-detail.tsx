"use client"

import React, { useState, useEffect } from "react"
import { getNotes } from "@/lib/data-service"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { ChevronRight, Clock, Flag, User } from "lucide-react"
import { TimelineBreadcrumb } from "./timeline-breadcrumb"
import { NoteRenderer } from "./notes/note-renderer"
import { NoteForm } from "../forms/note-form"
import type { TaskType, Objective, Program } from "../types"

interface TaskDetailProps {
  task: TaskType
  objective: Objective
  program: Program
  onNavigate: (level: "timeline" | "program" | "objective" | "task") => void
}

export function TaskDetail({ task, objective, program, onNavigate }: TaskDetailProps) {
  const [activeTab, setActiveTab] = useState("details")
  const [note, setNote] = useState<any>(null)
  const [showNoteForm, setShowNoteForm] = useState(false)

  useEffect(() => {
    (async () => {
      try {
        const fetchedNote = await getNotes(task.id)
        // Handle empty array response from API
        if (Array.isArray(fetchedNote) && fetchedNote.length === 0) {
          setNote(null)
        } else {
          setNote(fetchedNote)
        }
      } catch (error) {
        console.error("Failed to fetch notes:", error)
        setNote(null)
      }
    })()
  }, [task.id])

  const handleCreateNote = () => {
    setShowNoteForm(true)
  }

  const handleCloseNoteForm = () => {
    setShowNoteForm(false)
  }

  const handleNoteSuccess = async () => {
    setShowNoteForm(false)
    // Refetch notes after successful creation
    try {
      const fetchedNote = await getNotes(task.id)
      if (Array.isArray(fetchedNote) && fetchedNote.length === 0) {
        setNote(null)
      } else {
        setNote(fetchedNote)
      }
    } catch (error) {
      console.error("Failed to fetch notes after creation:", error)
    }
  }

  console.log("Task in TaskDetail:", task)
  console.log("Note found:", note)

  return (
    <div className="space-y-4">
      <TimelineBreadcrumb program={program} objective={objective} task={task} onNavigate={onNavigate} />

      <Card>
        <CardHeader className="pb-3">
          <div className="flex justify-between items-start">
            <div>
              <CardTitle className="text-xl font-semibold">{task.title}</CardTitle>
              <div className="flex items-center gap-2 mt-1 text-sm text-gray-500">
                <span className="flex items-center gap-1">
                  <User className="h-4 w-4" />
                  {task.assignedTo}
                </span>
                <span className="flex items-center gap-1">
                  <Clock className="h-4 w-4" />
                  {task.dueDate ? new Date(task.dueDate).toLocaleDateString("en-US", {
                    month: "short",
                    day: "numeric",
                    year: "numeric",
                  }) : 'N/A'}
                </span>
                <span className="flex items-center gap-1">
                  <Flag className="h-4 w-4" />
                  <Badge
                    className={
                      task.priority === "high"
                        ? "bg-red-100 text-red-800"
                        : task.priority === "medium"
                          ? "bg-amber-100 text-amber-800"
                          : "bg-green-100 text-green-800"
                    }
                  >
                    {task.priority.charAt(0).toUpperCase() + task.priority.slice(1)}
                  </Badge>
                </span>
                <Badge
                  className={
                    task.status === "completed"
                      ? "bg-green-100 text-green-800"
                      : task.status === "in_progress"
                        ? "bg-blue-100 text-blue-800"
                        : task.status === "pending"
                          ? "bg-amber-100 text-amber-800"
                          : "bg-gray-100 text-gray-800"
                  }
                >
                  {task.status
                    .split("_")
                    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
                    .join(" ")}
                </Badge>
              </div>
            </div>
            <div className="flex gap-2">
              <Button variant="outline" size="sm">
                Edit
              </Button>
              <Button variant="outline" size="sm" className="text-red-600 border-red-200 hover:bg-red-50">
                Delete
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
            <TabsList>
              <TabsTrigger value="details">Details</TabsTrigger>
              <TabsTrigger value="notes">Notes</TabsTrigger>
              <TabsTrigger value="activity">Activity</TabsTrigger>
            </TabsList>

            <TabsContent value="details" className="space-y-4">
              <div>
                <h3 className="text-sm font-medium text-gray-500 mb-2">Description</h3>
                <p className="text-gray-800">{task.description}</p>
              </div>

              <div>
                <h3 className="text-sm font-medium text-gray-500 mb-2">Related Items</h3>
                {task.relatedItems && task.relatedItems.length > 0 ? (
                  <ul className="space-y-2">
                    {task.relatedItems.map((item, index) => (
                      <li key={index} className="flex items-center gap-2 text-blue-600">
                        <ChevronRight className="h-4 w-4" />
                        <span>{item}</span>
                      </li>
                    ))}
                  </ul>
                ) : (
                  <p className="text-gray-500">No related items</p>
                )}
              </div>
            </TabsContent>

            <TabsContent value="notes">
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <h3 className="text-sm font-medium text-gray-500">Task Notes</h3>
                  <Button variant="outline" size="sm" onClick={handleCreateNote}>
                    Add Note
                  </Button>
                </div>

                {note ? (
                  <NoteRenderer note={note} />
                ) : (
                  <div className="text-center py-8 border border-dashed border-gray-200 rounded-md">
                    <p className="text-gray-500">No notes available for this task.</p>
                    <Button variant="outline" size="sm" className="mt-2" onClick={handleCreateNote}>
                      Create Note
                    </Button>
                  </div>
                )}
              </div>
            </TabsContent>

            <TabsContent value="activity">
              <div className="space-y-4">
                <h3 className="text-sm font-medium text-gray-500 mb-2">Activity Log</h3>
                <div className="space-y-3">
                  <div className="flex items-start gap-3 pb-3 border-b border-gray-100">
                    <div className="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center text-blue-600">
                      <User className="h-4 w-4" />
                    </div>
                    <div>
                      <p className="text-sm text-gray-800">
                        <span className="font-medium">Sarah Johnson</span> created this task
                      </p>
                      <p className="text-xs text-gray-500">Yesterday at 2:30 PM</p>
                    </div>
                  </div>
                  <div className="flex items-start gap-3 pb-3 border-b border-gray-100">
                    <div className="w-8 h-8 rounded-full bg-green-100 flex items-center justify-center text-green-600">
                      <Flag className="h-4 w-4" />
                    </div>
                    <div>
                      <p className="text-sm text-gray-800">
                        <span className="font-medium">System</span> changed priority from Medium to High
                      </p>
                      <p className="text-xs text-gray-500">Yesterday at 3:15 PM</p>
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
      
      <NoteForm
        isOpen={showNoteForm}
        onClose={handleCloseNoteForm}
        onSuccess={handleNoteSuccess}
        taskId={task.id}
        taskType={task.type}
      />
    </div>
  )
}


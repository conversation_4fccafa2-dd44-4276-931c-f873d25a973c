"use client"

import React, { useState, useEffect } from "react"
import { getObjectives } from "@/lib/data-service"
import { ChevronDown, ChevronRight, Plus } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import type { Program, WorkspacePersona } from "../types"
import { MonthGroup } from "./month-group"

interface ProgramCardProps {
  program: Program
  isExpanded?: boolean
  activePersona: WorkspacePersona
  onTaskSelect?: (taskId: string, objectiveId: string, programId: string) => void
}

export function ProgramCard({ program, isExpanded = true, activePersona, onTaskSelect }: ProgramCardProps) {
  const [expanded, setExpanded] = useState(isExpanded)
  const [objectives, setObjectives] = useState<any[]>([])

  useEffect(() => {
    (async () => {
      const fetchedObjectives = await getObjectives(program.id, activePersona)
      if (Array.isArray(fetchedObjectives)) setObjectives(fetchedObjectives)
    })()
  }, [program.id, activePersona])

  // Group objectives by month
  const objectivesByMonth = objectives.reduce(
    (acc, obj) => {
      const date = new Date(obj.startDate)
      const month = date.toLocaleString("default", { month: "long" })
      const year = date.getFullYear()
      const key = `${month}-${year}`

      if (!acc[key]) {
        acc[key] = {
          month,
          year,
          objectives: [],
        }
      }

      acc[key].objectives.push(obj)
      return acc
    },
    {} as Record<string, { month: string; year: number; objectives: typeof objectives }>,
  )

  // Sort months chronologically
  const sortedMonths = Object.values(objectivesByMonth).sort((a, b) => {
    const dateA = new Date(`${a.month} 1, ${a.year}`)
    const dateB = new Date(`${b.month} 1, ${b.year}`)
    return dateA.getTime() - dateB.getTime()
  })

  // Calculate progress
  const completedObjectives = objectives.filter((obj) => obj.status === "completed").length
  const progressPercentage = objectives.length > 0 ? Math.round((completedObjectives / objectives.length) * 100) : 0

  // Get status badge
  const getStatusBadge = (status: Program["status"]) => {
    switch (status) {
      case "completed":
        return <Badge className="bg-green-100 text-green-800 border-green-200">Completed</Badge>
      case "active":
        return <Badge className="bg-blue-100 text-blue-800 border-blue-200">Active</Badge>
      case "planning":
        return <Badge className="bg-amber-100 text-amber-800 border-amber-200">Planning</Badge>
      default:
        return null
    }
  }

  return (
    <div className="border border-gray-200 rounded-md bg-white shadow-sm">
      <div className="p-4 flex items-center justify-between bg-gray-50 border-b border-gray-200">
        <div className="flex items-center gap-2">
          <Button variant="ghost" size="sm" className="h-6 w-6 p-0" onClick={() => setExpanded(!expanded)}>
            {expanded ? (
              <ChevronDown className="h-4 w-4 text-gray-500" />
            ) : (
              <ChevronRight className="h-4 w-4 text-gray-500" />
            )}
          </Button>
          <h3 className="text-base font-medium text-gray-800">
            {program.quarter} {program.year}: {program.title}
          </h3>
          {getStatusBadge(program.status)}
        </div>
        <div className="flex items-center gap-3">
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-500">Owner: {program.owner}</span>
            <span className="text-sm text-gray-500">
              Objectives: {completedObjectives}/{objectives.length}
            </span>
          </div>
          <Button variant="outline" size="sm" className="border-gray-200">
            <Plus className="h-4 w-4 mr-1" />
            Add Objective
          </Button>
        </div>
      </div>

      {expanded && (
        <div className="p-4 space-y-4">
          <div>
            <div className="flex justify-between text-sm text-gray-500 mb-1">
              <span>Progress:</span>
              <span>{progressPercentage}%</span>
            </div>
            <Progress value={progressPercentage} className="h-2" />
          </div>
          <div className="space-y-4">
            {sortedMonths.map(({ month, year, objectives }) => (
              <MonthGroup
                key={`${month}-${year}`}
                month={month}
                year={year}
                objectives={objectives}
                programId={program.id}
                activePersona={activePersona}
                onTaskSelect={onTaskSelect}
              />
            ))}
          </div>
        </div>
      )}
    </div>
  )
}


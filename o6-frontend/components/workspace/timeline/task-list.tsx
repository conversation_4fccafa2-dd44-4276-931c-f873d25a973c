"use client"

import { <PERSON><PERSON><PERSON><PERSON>, Circle } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import type { TaskType } from "../types"

interface TaskListProps {
  tasks: TaskType[]
  objectiveId: string
  programId: string
  onTaskSelect?: (taskId: string, objectiveId: string, programId: string) => void
}

export function TaskList({ tasks, objectiveId, programId, onTaskSelect }: TaskListProps) {
  // Get priority badge
  const getPriorityBadge = (priority: TaskType["priority"]) => {
    switch (priority) {
      case "high":
        return <Badge className="bg-red-100 text-red-800 border-red-200">High</Badge>
      case "medium":
        return <Badge className="bg-amber-100 text-amber-800 border-amber-200">Medium</Badge>
      case "low":
        return <Badge className="bg-green-100 text-green-800 border-green-200">Low</Badge>
      default:
        return null
    }
  }

  // Get status icon
  const getStatusIcon = (status: TaskType["status"]) => {
    switch (status) {
      case "completed":
        return <CheckCircle className="h-5 w-5 text-green-600" />
      case "in_progress":
        return <Circle className="h-5 w-5 text-blue-600 fill-blue-100" />
      case "pending":
        return <Circle className="h-5 w-5 text-gray-400" />
      default:
        return <Circle className="h-5 w-5 text-gray-400" />
    }
  }

  return (
    <div className="space-y-2">
      {tasks.map((task) => (
        <div
          key={task.id}
          className="flex items-start gap-3 p-3 rounded-md hover:bg-gray-50 cursor-pointer"
          onClick={() => onTaskSelect && onTaskSelect(task.id, objectiveId, programId)}
        >
          <div className="mt-0.5">{getStatusIcon(task.status)}</div>
          <div className="flex-1 min-w-0">
            <div className="flex items-center justify-between">
              <h4 className="text-sm font-medium text-gray-800 truncate">{task.title}</h4>
              <div className="flex items-center gap-2 ml-2">
                {getPriorityBadge(task.priority)}
                <span className="text-xs text-gray-500 whitespace-nowrap">
                  {new Date(task.dueDate).toLocaleDateString("en-US", {
                    month: "short",
                    day: "numeric",
                  })}
                </span>
              </div>
            </div>
            <p className="text-xs text-gray-500 truncate">{task.description}</p>
          </div>
        </div>
      ))}
    </div>
  )
}


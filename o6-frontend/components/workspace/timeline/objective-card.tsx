"use client"

import React, { useState, useEffect } from "react"
import { getTasks } from "@/lib/data-service"
import { ChevronDown, ChevronRight, Plus } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import type { Objective, WorkspacePersona } from "../types"
import { TaskList } from "./task-list"

interface ObjectiveCardProps {
  objective: Objective
  programId: string
  activePersona: WorkspacePersona
  onTaskSelect?: (taskId: string, objectiveId: string, programId: string) => void
}

export function ObjectiveCard({ objective, programId, activePersona, onTaskSelect }: ObjectiveCardProps) {
  const [isExpanded, setIsExpanded] = useState(true)
  const [tasks, setTasks] = useState<any[]>([])

  useEffect(() => {
    (async () => {
      const fetchedTasks = await getTasks(objective.id, activePersona)
      if (Array.isArray(fetchedTasks)) setTasks(fetchedTasks)
    })()
  }, [objective.id, activePersona])

  // Calculate progress
  const completedTasks = tasks.filter((task) => task.status === "completed").length
  const progressPercentage = tasks.length > 0 ? Math.round((completedTasks / tasks.length) * 100) : 0

  // Get status badge
  const getStatusBadge = (status: Objective["status"]) => {
    switch (status) {
      case "completed":
        return <Badge className="bg-green-100 text-green-800 border-green-200">Completed</Badge>
      case "active":
        return <Badge className="bg-blue-100 text-blue-800 border-blue-200">Active</Badge>
      case "planning":
        return <Badge className="bg-amber-100 text-amber-800 border-amber-200">Planning</Badge>
      default:
        return null
    }
  }

  return (
    <div className="border border-gray-200 rounded-md bg-white shadow-sm">
      <div className="p-3 flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Button variant="ghost" size="sm" className="h-6 w-6 p-0" onClick={() => setIsExpanded(!isExpanded)}>
            {isExpanded ? (
              <ChevronDown className="h-4 w-4 text-gray-500" />
            ) : (
              <ChevronRight className="h-4 w-4 text-gray-500" />
            )}
          </Button>
          <h3 className="text-sm font-medium text-gray-800">{objective.title}</h3>
          {getStatusBadge(objective.status)}
        </div>
        <div className="flex items-center gap-3">
          <div className="flex items-center gap-2">
            <span className="text-xs text-gray-500">Owner: {objective.owner}</span>
            <span className="text-xs text-gray-500">
              Tasks: {completedTasks}/{tasks.length}
            </span>
          </div>
          <Button variant="outline" size="sm" className="h-7 text-xs border-gray-200">
            <Plus className="h-3 w-3 mr-1" />
            Add Task
          </Button>
        </div>
      </div>

      {isExpanded && (
        <div className="px-3 pb-3">
          <div className="mb-3">
            <div className="flex justify-between text-xs text-gray-500 mb-1">
              <span>Progress:</span>
              <span>{progressPercentage}%</span>
            </div>
            <Progress value={progressPercentage} className="h-2" />
          </div>
          <TaskList tasks={tasks} objectiveId={objective.id} programId={programId} onTaskSelect={onTaskSelect} />
        </div>
      )}
    </div>
  )
}


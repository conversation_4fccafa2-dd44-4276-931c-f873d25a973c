"use client"

import { ChevronRight } from "lucide-react"
import { Button } from "@/components/ui/button"

interface TimelineBreadcrumbProps {
  programName: string
  objectiveName: string
  taskName: string
  onNavigate: (level: "timeline" | "program" | "objective" | "task") => void
}

export function TimelineBreadcrumb({ programName, objectiveName, taskName, onNavigate }: TimelineBreadcrumbProps) {
  return (
    <nav className="flex items-center py-3 mb-4 border-b border-gray-200">
      <ol className="flex items-center space-x-2 text-sm">
        <li>
          <Button
            variant="link"
            className="p-0 h-auto text-gray-600 hover:text-forest-600"
            onClick={() => onNavigate("timeline")}
          >
            Timeline
          </Button>
        </li>
        <li className="flex items-center">
          <ChevronRight className="h-4 w-4 text-gray-400 mx-1" />
          <Button
            variant="link"
            className="p-0 h-auto text-gray-600 hover:text-forest-600"
            onClick={() => onNavigate("program")}
          >
            {programName}
          </Button>
        </li>
        <li className="flex items-center">
          <ChevronRight className="h-4 w-4 text-gray-400 mx-1" />
          <Button
            variant="link"
            className="p-0 h-auto text-gray-600 hover:text-forest-600"
            onClick={() => onNavigate("objective")}
          >
            {objectiveName}
          </Button>
        </li>
        <li className="flex items-center">
          <ChevronRight className="h-4 w-4 text-gray-400 mx-1" />
          <span className="text-gray-900 font-medium">{taskName}</span>
        </li>
      </ol>
    </nav>
  )
}


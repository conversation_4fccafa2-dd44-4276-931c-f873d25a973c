"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import {
  Menu,
  Edit,
  MoreHorizontal,
  Copy,
  Download,
  Share2,
  Mail,
  History,
  CalendarCheck,
  Star,
  Save,
  Bell,
  BarChart4,
} from "lucide-react"
import type { WorkspaceItem } from "../types"

interface WorkspaceHeaderProps {
  activeItem: WorkspaceItem | null
  editMode: boolean
  toggleSidebar: () => void
  setEditMode: (value: boolean) => void
  handleSaveItem: () => void
  handleConvertToTask: () => void
  toggleFavorite: (itemId: string) => void
}

export function WorkspaceHeader({
  activeItem,
  editMode,
  toggleSidebar,
  setEditMode,
  handleSaveItem,
  handleConvertToTask,
  toggleFavorite,
}: WorkspaceHeaderProps) {
  return (
    <header className="border-b border-border p-2 flex justify-between items-center bg-card">
      <div className="flex items-center">
        <Button variant="ghost" size="icon" onClick={toggleSidebar}>
          <Menu className="h-5 w-5" />
        </Button>
        {activeItem && (
          <div className="ml-2 flex items-center">
            <h1 className="font-medium truncate max-w-md">{activeItem.title}</h1>
            <span className="text-xs text-muted-foreground ml-2">
              Last updated: {activeItem.updatedAt.toLocaleDateString()}
            </span>
            <Badge variant="outline" className="ml-2">
              {activeItem.status.charAt(0).toUpperCase() + activeItem.status.slice(1)}
            </Badge>
          </div>
        )}
      </div>

      {activeItem && (
        <div className="flex items-center space-x-1">
          {/* Add the utility icons here */}
          <Button variant="ghost" size="icon" title="Notifications">
            <Bell className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="icon" title="Calendar">
            <CalendarCheck className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="icon" title="Analytics">
            <BarChart4 className="h-4 w-4" />
          </Button>

          {editMode ? (
            <>
              <Button variant="outline" size="sm" onClick={() => setEditMode(false)}>
                Cancel
              </Button>
              <Button size="sm" onClick={handleSaveItem}>
                <Save className="h-4 w-4 mr-1" />
                Save
              </Button>
            </>
          ) : (
            <>
              <Button variant="outline" size="sm" onClick={() => setEditMode(true)}>
                <Edit className="h-4 w-4 mr-1" />
                Edit
              </Button>

              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="icon">
                    <MoreHorizontal className="h-5 w-5" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem>
                    <Copy className="h-4 w-4 mr-2" />
                    Copy
                  </DropdownMenuItem>
                  <DropdownMenuItem>
                    <Download className="h-4 w-4 mr-2" />
                    Download
                  </DropdownMenuItem>
                  <DropdownMenuItem>
                    <Share2 className="h-4 w-4 mr-2" />
                    Share
                  </DropdownMenuItem>
                  <DropdownMenuItem>
                    <Mail className="h-4 w-4 mr-2" />
                    Send as Email
                  </DropdownMenuItem>
                  <DropdownMenuItem>
                    <History className="h-4 w-4 mr-2" />
                    Version History
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={handleConvertToTask}>
                    <CalendarCheck className="h-4 w-4 mr-2" />
                    Create Task from This
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => activeItem && toggleFavorite(activeItem.id)}>
                    <Star className="h-4 w-4 mr-2" />
                    {activeItem?.status === "favorite" ? "Remove from Favorites" : "Add to Favorites"}
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </>
          )}
        </div>
      )}
    </header>
  )
}


"use client"

import type React from "react"

import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Plus, Search } from "lucide-react"
import { cn } from "@/lib/utils"
import { TopNavigation } from "./top-navigation"
import { ModuleNavigation } from "./module-navigation"
import { NavigationSidebar } from "../common/navigation-sidebar"
import type { NavigationCategory } from "./navigation-data"
import type { WorkspacePersona, WorkspaceView } from "../types"

// User type as provided by NextAuth
interface User {
  name?: string | null
  email?: string | null
  image?: string | null
  id?: string
  organizationId?: string
}

interface WorkspaceSidebarProps {
  sidebarOpen: boolean
  activeCategory: NavigationCategory
  setActiveCategory: (category: NavigationCategory) => void
  activePersona: WorkspacePersona
  setActivePersona: (persona: WorkspacePersona) => void
  activeView: WorkspaceView
  setActiveView: (view: WorkspaceView) => void
  searchQuery: string
  handleSearch: (e: React.ChangeEvent<HTMLInputElement>) => void
  user?: User
}

export function WorkspaceSidebar({
  sidebarOpen,
  activeCategory,
  setActiveCategory,
  activePersona,
  setActivePersona,
  activeView,
  setActiveView,
  searchQuery,
  handleSearch,
  user,
}: WorkspaceSidebarProps) {
  return (
    <div
      className={cn(
        "transition-all duration-300 bg-forest-600 text-white",
        sidebarOpen ? "w-64" : "w-0 overflow-hidden",
      )}
    >
      <div className="h-full flex flex-col">
        {/* Top Navigation */}
        <div className="p-4 flex justify-between items-center border-b border-white/10">
          <TopNavigation
            activeCategory={activeCategory}
            setActiveCategory={setActiveCategory}
            activePersona={activePersona}
            setActivePersona={setActivePersona}
          />

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button size="sm" className="bg-white text-forest-600 hover:bg-cream-100">
                <Plus className="h-4 w-4 mr-1" />
                New
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="bg-white border border-gray-100 shadow-lg">
              {/* Menu items would change based on active category and persona */}
              {activeCategory === "hcm" && activePersona === "recruiter" && (
                <>
                  <DropdownMenuItem className="hover:bg-cream-100 hover:text-forest-600">
                    Job Description
                  </DropdownMenuItem>
                  <DropdownMenuItem className="hover:bg-cream-100 hover:text-forest-600">
                    Boolean Search
                  </DropdownMenuItem>
                  <DropdownMenuItem className="hover:bg-cream-100 hover:text-forest-600">
                    Candidate Profile
                  </DropdownMenuItem>
                </>
              )}
              {activeCategory === "hcm" && activePersona === "hr-generalist" && (
                <>
                  <DropdownMenuItem className="hover:bg-cream-100 hover:text-forest-600">
                    Employee Record
                  </DropdownMenuItem>
                  <DropdownMenuItem className="hover:bg-cream-100 hover:text-forest-600">
                    Policy Document
                  </DropdownMenuItem>
                  <DropdownMenuItem className="hover:bg-cream-100 hover:text-forest-600">
                    Compliance Report
                  </DropdownMenuItem>
                </>
              )}
              {activeCategory === "hcm" && activePersona === "benefits" && (
                <>
                  <DropdownMenuItem className="hover:bg-cream-100 hover:text-forest-600">
                    Benefits Plan
                  </DropdownMenuItem>
                  <DropdownMenuItem className="hover:bg-cream-100 hover:text-forest-600">
                    Enrollment Campaign
                  </DropdownMenuItem>
                  <DropdownMenuItem className="hover:bg-cream-100 hover:text-forest-600">
                    Vendor Contract
                  </DropdownMenuItem>
                </>
              )}
              {activeCategory === "hcm" && activePersona === "learning" && (
                <>
                  <DropdownMenuItem className="hover:bg-cream-100 hover:text-forest-600">
                    Training Program
                  </DropdownMenuItem>
                  <DropdownMenuItem className="hover:bg-cream-100 hover:text-forest-600">
                    Learning Path
                  </DropdownMenuItem>
                  <DropdownMenuItem className="hover:bg-cream-100 hover:text-forest-600">
                    Skills Assessment
                  </DropdownMenuItem>
                </>
              )}
              {activeCategory === "crm" && (
                <>
                  <DropdownMenuItem className="hover:bg-cream-100 hover:text-forest-600">New Client</DropdownMenuItem>
                  <DropdownMenuItem className="hover:bg-cream-100 hover:text-forest-600">New Job</DropdownMenuItem>
                </>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        <div className="px-4 py-3">
          <div className="relative">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-white/60" />
            <Input
              placeholder="Search workspace..."
              className="pl-8 bg-forest-700/50 border-white/10 text-white placeholder:text-white/60 focus:border-white focus:ring-white/30"
              value={searchQuery}
              onChange={handleSearch}
            />
          </div>
        </div>

        {/* Module Navigation */}
        <ModuleNavigation
          activeCategory={activeCategory}
          activePersona={activePersona}
          setActivePersona={setActivePersona}
        />

        {/* Workspace Views Navigation */}
        <NavigationSidebar activeView={activeView} setActiveView={setActiveView} />

        <div className="flex-1"></div>

        {/* User Profile */}
        <div className="p-4 border-t border-white/10">
          <div className="flex items-center">
          <div className="w-8 h-8 rounded-full bg-white/20 flex items-center justify-center text-white font-medium">
              {user?.name ? user.name.charAt(0).toUpperCase() : user?.email?.charAt(0).toUpperCase() || "U"}
            </div>
            <div className="ml-2">
              <div className="text-sm font-medium text-white">{user?.name || "User"}</div>
              <div className="text-xs text-white/60">{user?.email || "No email"}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}


"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { Search, Plus, Hexagon } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { cn } from "@/lib/utils"
import { useSearchParams } from "next/navigation"

// Import workspace components
import { RecruiterWorkspace } from "./personas/recruiter/recruiter-workspace"
import { HRGeneralistWorkspace } from "./personas/hr-generalist/hr-generalist-workspace"
import { BenefitsWorkspace } from "./personas/benefits/benefits-workspace"
import { LearningWorkspace } from "./personas/learning/learning-workspace"
import { WorkspaceHeader } from "./common/workspace-header"
import { NavigationSidebar } from "./common/navigation-sidebar"
import { FloatingAssistant } from "./ai-assistant/floating-assistant"
import { WelcomeScreen } from "./common/welcome-screen"
import { TimelineView } from "./views/timeline-view"
import { ReportsView } from "./views/reports-view"
import { ClientsModule } from "../clients/clients-module"
import { SettingsModule } from "../settings/settings-module"
import { TaskDetailView } from "./task/task-detail-view"

// Import types
import type { WorkspaceItem, WorkspacePersona, WorkspaceView, TaskType, Client } from "./types"

// Define NavigationCategory type
type NavigationCategory = "home" | "recruitment" | "hr" | "benefits" | "learning" | "clients" | "settings"

// User type as provided by NextAuth
interface User {
  name?: string | null
  email?: string | null
  image?: string | null
  id?: string
  organizationId?: string
}

// Add support for "home" as an initialCategory
interface WorkspaceModuleProps {
  children?: React.ReactNode
  title?: string
  navigation?: any[]
  module?: string
  initialPersona?: WorkspacePersona
  initialCategory?: NavigationCategory
  initialView?: WorkspaceView
  user?: User
}

export function WorkspaceModule({ 
  children, 
  title = "Workspace", 
  navigation = [], 
  module = "home",
  initialPersona = "recruiter",
  initialView = "dashboard",
  user
}: WorkspaceModuleProps) {
  const searchParams = useSearchParams()
  const [sidebarOpen, setSidebarOpen] = useState(true)
  const [activeItem, setActiveItem] = useState<WorkspaceItem | null>(null)
  const [activeTask, setActiveTask] = useState<TaskType | null>(null)
  const [activeClient, setActiveClient] = useState<Client | null>(null)
  const [searchQuery, setSearchQuery] = useState("")
  const [activePersona, setActivePersona] = useState<WorkspacePersona>(initialPersona)
  const [activeView, setActiveView] = useState<WorkspaceView>(initialView)
  const [editMode, setEditMode] = useState(false)
  const [showClientsModule, setShowClientsModule] = useState(module === "clients")
  const [showSettingsModule, setShowSettingsModule] = useState(module === "settings")

  // Check for URL parameters to determine which module to show
  useEffect(() => {
    const moduleParam = searchParams.get("module")
    const persona = searchParams.get("persona") as WorkspacePersona | null
    const path = window.location.pathname

    // Reset all module states first
    setShowClientsModule(false)
    setShowSettingsModule(false)

    // Determine module based on URL parameters or pathname
    if (moduleParam === "crm" || path.includes("/crm")) {
      setShowClientsModule(true)
      // Don't force persona change for CRM - let it maintain current persona
    } else if (moduleParam === "settings" || path.includes("/settings")) {
      setShowSettingsModule(true)
    }

    // Only set persona if explicitly provided in URL or if no persona is set
    if (persona && ["recruiter", "hr-generalist", "benefits", "learning"].includes(persona)) {
      setActivePersona(persona as WorkspacePersona)
    } else if (!persona && module === "workspace" && !moduleParam) {
      // Default to recruiter only for the main workspace view with no module
      setActivePersona("recruiter")
    }
  }, [searchParams, module])

  // Add this after the useEffect hook to make setShowClientsModule globally available
  useEffect(() => {
    // Make setShowClientsModule available globally for navigation
    window.setShowClientsModule = setShowClientsModule

    return () => {
      delete window.setShowClientsModule
    }
  }, [setShowClientsModule])

  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen)
  }

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value)
  }

  const handleSaveItem = () => {
    // Implementation would depend on the active persona
    console.log("Saving item for persona:", activePersona)
    setEditMode(false)
  }

  const handleTaskSelect = (task: TaskType) => {
    setActiveTask(task)
    setActiveItem(null) // Clear any active item
    setActiveClient(null)
    // Don't clear module states when selecting tasks
  }

  const resetTaskSelection = () => {
    setActiveTask(null)
  }

  // In the renderWorkspaceContent function, update the task details rendering:
  const renderWorkspaceContent = () => {
    // If there are children, render those (takes highest priority)
    if (children) {
      return children
    }
    
    // If a task is selected, show task details with the new TaskDetailView component
    if (activeTask) {
      return <TaskDetailView task={activeTask} onClose={resetTaskSelection} />
    }

    // If clients module is active, handle different view modes
    if (showClientsModule) {
      // If Timeline or Reports view is selected, show those views with client context
      if (activeView === "timeline") {
        return <TimelineView activePersona="recruiter" isClientModule={true} onTaskSelect={handleTaskSelect} resetTaskSelection={resetTaskSelection} />
      } else if (activeView === "reports") {
        return <ReportsView activePersona="recruiter" isClientModule={true} />
      } else {
        // Always show the Clients module for dashboard view when in CRM
        return <ClientsModule user={user} />
      }
    }

    // If settings module should be shown
    if (showSettingsModule) {
      return <SettingsModule />
    }

    // If an item is selected, show item details (only for workspace, not CRM/Settings)
    if (activeItem && !showClientsModule && !showSettingsModule) {
      switch (activePersona) {
        case "recruiter":
          return <RecruiterWorkspace activeItem={activeItem} editMode={editMode} />
        case "hr-generalist":
          return <HRGeneralistWorkspace activeItem={activeItem} editMode={editMode} />
        case "benefits":
          return <BenefitsWorkspace activeItem={activeItem} editMode={editMode} />
        case "learning":
          return <LearningWorkspace activeItem={activeItem} editMode={editMode} />
        default:
          return <div>Select a workspace</div>
      }
    }

    // Otherwise, show the active view (only for workspace, not CRM/Settings)
    if (!showClientsModule && !showSettingsModule) {
      switch (activeView) {
        case "dashboard":
          switch (activePersona) {
            case "recruiter":
              return <RecruiterWorkspace activeItem={null} editMode={false} />
            case "hr-generalist":
              return <HRGeneralistWorkspace activeItem={null} editMode={false} />
            case "benefits":
              return <BenefitsWorkspace activeItem={null} editMode={false} />
            case "learning":
              return <LearningWorkspace activeItem={null} editMode={false} />
            default:
              return <WelcomeScreen activePersona={activePersona} onCreateItem={() => {}} />
          }
        case "timeline":
          return <TimelineView activePersona={activePersona} onTaskSelect={handleTaskSelect} resetTaskSelection={resetTaskSelection} />
        case "reports":
          return <ReportsView activePersona={activePersona} />
        default:
          return <WelcomeScreen activePersona={activePersona} onCreateItem={() => {}} />
      }
    }

    // Fallback to welcome screen
    return <WelcomeScreen activePersona={activePersona} onCreateItem={() => {}} />
  }

  return (
    <div className="flex h-screen overflow-hidden">
      {/* Main container with clean design */}
      <div className="w-full h-full max-w-[1400px] mx-auto my-4 rounded-xl overflow-hidden shadow-forest flex bg-white">
        {/* Sidebar */}
        <div
          className={cn(
            "transition-all duration-300 bg-forest-600 text-white",
            sidebarOpen ? "w-64" : "w-0 overflow-hidden",
          )}
        >
          <div className="h-full flex flex-col">
            <div className="p-4 border-b border-white/10">
              <div className="flex items-center">
                <Hexagon className="h-6 w-6 mr-2 text-white fill-forest-300 stroke-[1.5]" />
                <h2 className="font-semibold text-lg text-white">
                  {title}
                </h2>
              </div>
            </div>

            {/* Navigation Sidebar - keep the existing structure */}
            <NavigationSidebar 
              activeView={activeView} 
              setActiveView={setActiveView} 
              onNavigate={resetTaskSelection}
              module={showSettingsModule ? "settings" : showClientsModule ? "crm" : module} 
            />

            <div className="flex-1"></div>

            {/* User Profile */}
            <div className="p-4 border-t border-white/10">
              <div className="flex items-center">                <div className="w-8 h-8 rounded-full bg-white/20 flex items-center justify-center text-white font-medium">
                  {user?.name ? user.name.charAt(0).toUpperCase() : user?.email?.charAt(0).toUpperCase() || "U"}
                </div>
                <div className="ml-2">
                  <div className="text-sm font-medium text-white">{user?.name || "User"}</div>
                  <div className="text-xs text-white/60">{user?.email || "No email"}</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 flex flex-col overflow-hidden bg-white">
          {/* Header */}          <WorkspaceHeader
            activeItem={activeItem}
            editMode={editMode}
            toggleSidebar={toggleSidebar}
            setEditMode={setEditMode}
            handleSaveItem={handleSaveItem}
            activePersona={activePersona}
            setActivePersona={setActivePersona}
            showClientsModule={showClientsModule}
            setShowClientsModule={setShowClientsModule}
            setActiveTask={setActiveTask}
            user={user}
          />

          {/* Content Area */}
          <div className="flex-1 flex overflow-hidden">
            <div className="flex-1 overflow-auto p-6">
              <div className="max-w-4xl mx-auto">{renderWorkspaceContent()}</div>
            </div>
          </div>

          {/* Footer - simplified */}
          <div className="border-t border-gray-200/50 p-1 bg-white"></div>
        </div>
      </div>

      {/* Floating Assistant - contextual to the active persona */}
      <FloatingAssistant 
        activeItem={activeItem} 
        activePersona={activePersona} 
        activeClient={activeClient} 
        activeTask={activeTask}
        isClientModule={showClientsModule} 
        isSettingsModule={showSettingsModule}
      />
    </div>
  )
}


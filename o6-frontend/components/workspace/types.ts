export type TaskType = {
  id: string
  objectiveId: string
  title: string
  description: string
  dueDate: Date
  status: "pending" | "in_progress" | "completed"
  priority: "low" | "medium" | "high"
  assignedTo: string
  relatedItems: string[] // IDs of related workspace items
  persona: WorkspacePersona
  type?: "meeting" | "posting" | "sourcing" | "screening" | "job_description" | "other"
  noteId?: string
}

export type Client = {
  id: string
  orgId: string
  name: string
  industry: string
  location: string
  contactPerson: string
  contactEmail: string
  contactPhone: string
  status: "active" | "inactive" | "lead" | "prospect"
  notes: string
  createdAt: Date
  updatedAt: Date
  openJobs: number
  totalPlacements: number
}

export type Job = {
  id: string
  title: string
  content: string
  tags: string[]
  createdAt: Date
  updatedAt: Date
  type: "job_description" | "boolean_search" | "candidate_profile" | "meeting_notes"
  status: "active" | "inactive" | "draft" | "archived"
  category: string
  clientId: string
  department: string
  location: string
  salary: string
  experience: string
  persona: WorkspacePersona
  versions: {
    id: string
    content: string
    createdAt: Date
  }[]
}

export type WorkspaceItem = {
  id: string
  title: string
  content: string
  tags: string[]
  createdAt: Date
  updatedAt: Date
  type: "job_description" | "boolean_search" | "candidate_profile" | "meeting_notes"
  status: "active" | "inactive" | "draft" | "archived" | "favorite"
  category: string
  persona: WorkspacePersona
  versions: {
    id: string
    content: string
    createdAt: Date
  }[]
}

export type BlockType = {
  id: string
  type:
    | "text"
    | "heading"
    | "subheading"
    | "bullet"
    | "numbered"
    | "job_details"
    | "boolean_search"
    | "candidate_rating"
    | "divider"
  content: string
}

export type WorkspacePersona = "recruiter" | "hr-generalist" | "benefits" | "learning"

export type WorkspaceView = "dashboard" | "timeline" | "reports"

export type Objective = {
  id: string
  programId: string
  title: string
  description: string
  month: number
  year: number
  status: "not_started" | "in_progress" | "completed"
  progress: number
  owner: string
  createdAt: Date
  updatedAt: Date
  startDate: Date
  persona: WorkspacePersona
}

export type Program = {
  id: string
  title: string
  description: string
  quarter: "Q1" | "Q2" | "Q3" | "Q4"
  year: number
  status: "planning" | "active" | "completed" | "on_hold"
  progress: number
  owner: string
  createdAt: Date
  updatedAt: Date
  persona: WorkspacePersona
}

export type MeetingNote = {
  id: string
  taskId: string
  type: "meeting"
  lastEdited: Date
  title: string
  date: Date
  attendees: string[]
  roleRequirements: string[]
  compensation: {
    budget: string
    benefits: string
  }
  timeline: {
    startDate: string
  }
  nextSteps: string[]
}

export type PostingNote = {
  id: string
  taskId: string
  type: "posting"
  lastEdited: Date
  title: string
  overview: string
  company: string
  location: string
  salaryRange: string
  experience: string
  responsibilities: string[]
}

export type SourcingNote = {
  id: string
  taskId: string
  type: "sourcing"
  lastEdited: Date
  title: string
  booleanString: string
  analysis: string
  expectedResults: string
}

export type ScreeningNote = {
  id: string
  taskId: string
  type: "screening"
  lastEdited: Date
  candidateName: string
  professionalSummary: string
  skills: {
    name: string
    rating: number
  }[]
  screeningNotes: string
}

export type Note = MeetingNote | PostingNote | SourcingNote | ScreeningNote

export type NavigationCategory = "hcm" | "crm" | "settings"

export interface NavigationItem {
  title: string
  href: string
  icon: any
  description: string
}


"use client"

import { But<PERSON> } from "@/components/ui/button"
import { cn } from "@/lib/utils"
import { workspaceViews } from "../navigation/navigation-data"
import type { WorkspaceView } from "../types"
import * as LucideIcons from "lucide-react"

// Helper function to get the icon component by name
const getIconByName = (iconName: string) => {
  const Icon = (LucideIcons as any)[iconName] || LucideIcons.CircleDot // Fallback to Circle if icon not found
  return Icon
}

interface NavigationSidebarProps {
  activeView: WorkspaceView
  setActiveView: (view: WorkspaceView) => void
  onNavigate?: () => void
  module?: string
}

export function NavigationSidebar({ activeView, setActiveView, onNavigate, module = "workspace" }: NavigationSidebarProps) {
  // ONLY hide Timeline and Reports in Home module and Settings module, show all for others
  const filteredViews = module === "home" || module === "settings"
    ? workspaceViews.filter(view => view.id === "dashboard")
    : workspaceViews;

  return (
    <div className="px-2 py-4">
      <div className="space-y-1">
        {filteredViews.map((view) => {
          const Icon = getIconByName(view.icon)
          return (
            <Button
              key={view.id}
              variant="ghost"
              className={cn(
                "w-full justify-start mb-1 font-medium text-white/80 hover:bg-white/10 hover:text-white",
                activeView === view.id && "bg-white/20 text-white",
              )}
              onClick={() => {
                onNavigate?.()
                setActiveView(view.id as WorkspaceView)
              }}
            >
              <Icon className="h-4 w-4 mr-2" />
              <span>{view.name}</span>
            </Button>
          )
        })}
      </div>
    </div>
  )
}


"use client"

import { useState, useEffect } from "react"
import { Grid3x3, X } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { cn } from "@/lib/utils"
import type { WorkspacePersona } from "../types"
import { useRouter, usePathname, useSearchParams } from "next/navigation"

interface AppLauncherProps {
  activePersona: WorkspacePersona
  setActivePersona: (persona: WorkspacePersona) => void
  setShowClientsModule?: (show: boolean) => void
  setActiveTask?: (task: null) => void
}

// Define app categories and their apps
interface AppCategory {
  id: string
  name: string
  apps: App[]
}

interface App {
  id: string
  title: string
  icon: string
  isActive: boolean
  persona: WorkspacePersona | "all"
  path?: string
  disabled?: boolean
}

export function AppLauncher({ activePersona, setActivePersona, setShowClientsModule, setActiveTask }: AppLauncherProps) {
  const [isOpen, setIsOpen] = useState(false)
  const router = useRouter()
  const pathname = usePathname()
  const searchParams = useSearchParams()
  const [appCategories, setAppCategories] = useState<AppCategory[]>([])
  const [mounted, setMounted] = useState(false)
  
  // Handle mounting to avoid hydration issues
  useEffect(() => {
    setMounted(true)
  }, [])
  
  // Move the dynamic app categories creation to useEffect to avoid hydration issues
  useEffect(() => {
    if (!mounted) return;
    
    // Get module query parameter
    const moduleParam = searchParams.get('module')
    const personaParam = searchParams.get('persona')
    
    // Create app categories with their respective apps
    setAppCategories([
      {
        id: "home",
        name: "Home",
        apps: [
          {
            id: "home",
            title: "Home",
            icon: "🏠",
            isActive: pathname.startsWith("/home"),
            persona: "all",
            path: "/home/<USER>",
          },
        ],
      },
      {
        id: "hcm",
        name: "HCM",
        apps: [
          {
            id: "recruiting",
            title: "Recruiting",
            icon: "👥",
            isActive: pathname === "/workspace" && personaParam === "recruiter" && !moduleParam,
            persona: "recruiter",
            path: "/workspace?persona=recruiter",
          },
          {
            id: "hr",
            title: "HR",
            icon: "📋",
            isActive: pathname === "/workspace" && personaParam === "hr-generalist",
            persona: "hr-generalist",
            path: "/workspace?persona=hr-generalist",
          },
          {
            id: "benefits",
            title: "Benefits",
            icon: "❤️",
            isActive: pathname === "/workspace" && personaParam === "benefits",
            persona: "benefits",
            path: "/workspace?persona=benefits",
          },
          {
            id: "learning",
            title: "L&D",
            icon: "🎓",
            isActive: pathname === "/workspace" && personaParam === "learning",
            persona: "learning",
            path: "/workspace?persona=learning",
          },
        ],
      },
      {
        id: "crm",
        name: "CRM",
        apps: [
          {
            id: "clients",
            title: "Clients",
            icon: "🏢",
            isActive: moduleParam === "crm",
            persona: "recruiter",
            path: "/workspace?module=crm", // Updated to use workspace with module parameter
          },
        ],
      },
      {
        id: "other",
        name: "Other",
        apps: [
          {
            id: "settings",
            title: "Settings",
            icon: "⚙️",
            isActive: moduleParam === "settings",
            persona: "all",
            path: "/workspace?module=settings", // Updated to use workspace with module parameter
          },
          {
            id: "payroll",
            title: "Payroll",
            icon: "💰",
            isActive: false,
            persona: "all",
            disabled: true,
          },
          {
            id: "compliance",
            title: "Compliance",
            icon: "⚖️",
            isActive: false,
            persona: "all",
            disabled: true,
          },
        ],
      },
    ]);
  }, [pathname, searchParams, activePersona, mounted]);

  const handleAppSelect = (app: App) => {
    if (app.disabled) return

    // Handle special case for Clients app first
    if (app.id === "clients" && setShowClientsModule) {
      setShowClientsModule(true)
    } else if (setShowClientsModule && app.id !== "clients") {
      setShowClientsModule(false)
    }

    // Clear active task when switching apps
    if (setActiveTask) {
      setActiveTask(null)
    }

    // Navigate to the app's path if it has one
    if (app.path) {
      router.push(app.path)
    } else if (app.persona !== "all") {
      setActivePersona(app.persona as WorkspacePersona)
    }

    setIsOpen(false)
  }

  return (
    <div>
      <Button
        variant="ghost"
        size="icon"
        onClick={() => setIsOpen(!isOpen)}
        className="text-gray-600 hover:text-forest-600 hover:bg-cream-100 relative"
        title="Apps"
      >
        <Grid3x3 className="h-5 w-5" />
      </Button>

      {isOpen && mounted && (
        <>
          {/* Backdrop */}
          <div className="fixed inset-0 bg-black/20 z-50" onClick={() => setIsOpen(false)} />

          {/* App Launcher Modal */}
          <Card className="absolute right-0 top-12 z-50 w-[380px] p-4 shadow-lg border border-gray-200/50 bg-white rounded-lg">
            <div className="flex justify-between items-center mb-3">
              <h3 className="text-sm font-medium text-gray-700">O6 Apps</h3>
              <Button variant="ghost" size="icon" onClick={() => setIsOpen(false)} className="h-6 w-6">
                <X className="h-4 w-4" />
              </Button>
            </div>

            {/* Render app categories */}
            {appCategories.map((category) => (
              <div key={category.id} className="mb-4">
                <h4 className="text-xs font-semibold text-gray-500 mb-2">{category.name}</h4>
                <div className="grid grid-cols-3 gap-2">
                  {category.apps.map((app) => (
                    <AppButton
                      key={app.id}
                      title={app.title}
                      icon={app.icon}
                      isActive={app.isActive}
                      onClick={() => handleAppSelect(app)}
                      disabled={app.disabled}
                    />
                  ))}
                </div>
              </div>
            ))}
          </Card>
        </>
      )}
    </div>
  )
}

interface AppButtonProps {
  title: string
  icon: string
  isActive: boolean
  onClick: () => void
  disabled?: boolean
}

function AppButton({ title, icon, isActive, onClick, disabled = false }: AppButtonProps) {
  return (
    <button
      className={cn(
        "flex flex-col items-center justify-center p-3 rounded-lg transition-colors",
        isActive ? "bg-forest-100 text-forest-600" : "hover:bg-cream-100 hover:text-forest-600",
        disabled && "opacity-50 cursor-not-allowed hover:bg-transparent hover:text-inherit",
      )}
      onClick={onClick}
      disabled={disabled}
    >
      <span className="text-2xl mb-1">{icon}</span>
      <span className="text-xs font-medium">{title}</span>
    </button>
  )
}


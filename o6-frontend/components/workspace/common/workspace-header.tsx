"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import {
  Menu,
  Edit,
  MoreHorizontal,
  Copy,
  Download,
  Share2,
  Mail,
  History,
  CalendarCheck,
  Star,
  Save,
  Bell,
  BarChart4,
  Search,
  User
} from "lucide-react"
import { AppLauncher } from "./app-launcher"
import type { WorkspaceItem, WorkspacePersona } from "../types"
import { usePathname, useSearchParams } from "next/navigation"
import { useState } from "react"

// User type as provided by NextAuth
interface User {
  name?: string | null
  email?: string | null
  image?: string | null
  id?: string
  organizationId?: string
}

interface WorkspaceHeaderProps {
  activeItem: WorkspaceItem | null
  editMode: boolean
  toggleSidebar: () => void
  setEditMode: (value: boolean) => void
  handleSaveItem: () => void
  activePersona: WorkspacePersona
  setActivePersona: (persona: WorkspacePersona) => void
  showClientsModule?: boolean
  setShowClientsModule?: (show: boolean) => void
  setActiveTask?: (task: null) => void
  user?: User
}

export function WorkspaceHeader({
  activeItem,
  editMode,
  toggleSidebar,
  setEditMode,
  handleSaveItem,
  activePersona,
  setActivePersona,
  showClientsModule = false,
  setShowClientsModule,
  setActiveTask,
  user,
}: WorkspaceHeaderProps) {
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const isHomePage = pathname.startsWith('/home');
  const moduleParam = searchParams.get('module');
  const personaParam = searchParams.get('persona');
  const [searchQuery, setSearchQuery] = useState("");

  // Helper function to determine the current module title
  const getCurrentTitle = () => {
    if (isHomePage) return "Home";
    if (moduleParam === "crm") return "Clients";
    if (moduleParam === "settings") return "Settings";
    
    // If no module parameter, determine by persona
    if (personaParam === "hr-generalist") return "HR Management";
    if (personaParam === "benefits") return "Benefits";
    if (personaParam === "learning") return "Learning & Development";
    
    // Default for recruiter or no specific persona
    return "Recruiting";
  };

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
    // Implement global search functionality here
  };

  return (
    <header className="border-b border-gray-200/50 p-2 flex justify-between items-center bg-white">
      <div className="flex items-center">
        <Button
          variant="ghost"
          size="icon"
          onClick={toggleSidebar}
          className="text-gray-600 hover:text-forest-600 hover:bg-cream-100"
        >
          <Menu className="h-5 w-5" />
        </Button>
        {activeItem ? (
          <div className="ml-2 flex items-center">
            <h1 className="font-medium truncate max-w-md text-gray-800">{activeItem.title}</h1>
            <span className="text-xs text-gray-500 ml-2">
              Last updated: {activeItem.updatedAt.toLocaleDateString()}
            </span>
            <Badge variant="outline" className="ml-2 bg-forest-100 text-forest-600 border-forest-200">
              {activeItem.status.charAt(0).toUpperCase() + activeItem.status.slice(1)}
            </Badge>
          </div>
        ) : (
          <div className="ml-2 flex items-center">
            <h1 className="font-medium text-gray-800">
              {getCurrentTitle()}
            </h1>
          </div>
        )}
      </div>

      {/* Global Search Box */}
      <div className="flex-1 max-w-xl mx-4">
        <div className="relative">
          <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            placeholder="Search across O6..."
            className="pl-8 bg-gray-50 border-gray-200 text-gray-800 placeholder:text-gray-400 focus:border-forest-200 focus:ring-forest-200/30 w-full"
            value={searchQuery}
            onChange={handleSearch}
          />
        </div>
      </div>      <div className="flex items-center space-x-1">
        {/* Utility icons */}
        <Button
          variant="ghost"
          size="icon"
          title="Notifications"
          className="text-gray-600 hover:text-forest-600 hover:bg-cream-100"
        >
          <Bell className="h-4 w-4" />
        </Button>
        <Button
          variant="ghost"
          size="icon"
          title="Calendar"
          className="text-gray-600 hover:text-forest-600 hover:bg-cream-100"
        >
          <CalendarCheck className="h-4 w-4" />
        </Button>
        
        {/* User avatar dropdown */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button 
              variant="ghost" 
              className="relative h-8 w-8 rounded-full ml-2"
              title={user?.name || user?.email || "User profile"}
            >
              {user?.image ? (
                <img 
                  src={user.image}
                  alt={user.name || "User avatar"} 
                  className="h-8 w-8 rounded-full object-cover"
                />
              ) : (
                <div className="flex h-8 w-8 rounded-full text-forest-700 items-center justify-center font-medium">
                  <User className="h-5 w-5" />
                </div>
              )}
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-56">
            <div className="px-2 py-2 border-b">
              <p className="text-sm font-medium">{user?.name || "User"}</p>
              <p className="text-xs text-gray-500 truncate">{user?.email}</p>
            </div>
            <DropdownMenuItem asChild>
              <a href="/profile" className="cursor-pointer">Profile</a>
            </DropdownMenuItem>
            <DropdownMenuItem asChild>
              <a href="/settings" className="cursor-pointer">Settings</a>
            </DropdownMenuItem>
            <DropdownMenuItem 
              className="text-red-600 focus:bg-red-50 focus:text-red-700" 
              onClick={() => {
                // Import dynamically to avoid ssr issues
                import("next-auth/react").then(({ signOut }) => signOut({ redirectTo: "/login" }));
              }}
            >
              Sign out
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
        
        <Button
          variant="ghost"
          size="icon"
          title="Analytics"
          className="text-gray-600 hover:text-forest-600 hover:bg-cream-100"
        >
          <BarChart4 className="h-4 w-4" />
        </Button>

        {activeItem && (
          <>
            {editMode ? (
              <>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setEditMode(false)}
                  className="border-gray-200 text-gray-700 hover:bg-cream-100"
                >
                  Cancel
                </Button>
                <Button size="sm" onClick={handleSaveItem} className="bg-forest-600 text-white hover:bg-forest-700">
                  <Save className="h-4 w-4 mr-1" />
                  Save
                </Button>
              </>
            ) : (
              <>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setEditMode(true)}
                  className="border-gray-200 text-gray-700 hover:border-forest-600 hover:text-forest-600"
                >
                  <Edit className="h-4 w-4 mr-1" />
                  Edit
                </Button>

                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="text-gray-600 hover:text-forest-600 hover:bg-cream-100"
                    >
                      <MoreHorizontal className="h-5 w-5" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="bg-white border border-gray-100 shadow-lg">
                    <DropdownMenuItem className="hover:bg-cream-100 hover:text-forest-600">
                      <Copy className="h-4 w-4 mr-2" />
                      Copy
                    </DropdownMenuItem>
                    <DropdownMenuItem className="hover:bg-cream-100 hover:text-forest-600">
                      <Download className="h-4 w-4 mr-2" />
                      Download
                    </DropdownMenuItem>
                    <DropdownMenuItem className="hover:bg-cream-100 hover:text-forest-600">
                      <Share2 className="h-4 w-4 mr-2" />
                      Share
                    </DropdownMenuItem>
                    <DropdownMenuItem className="hover:bg-cream-100 hover:text-forest-600">
                      <Mail className="h-4 w-4 mr-2" />
                      Send as Email
                    </DropdownMenuItem>
                    <DropdownMenuItem className="hover:bg-cream-100 hover:text-forest-600">
                      <History className="h-4 w-4 mr-2" />
                      Version History
                    </DropdownMenuItem>
                    <DropdownMenuItem className="hover:bg-cream-100 hover:text-forest-600">
                      <CalendarCheck className="h-4 w-4 mr-2" />
                      Create Task from This
                    </DropdownMenuItem>
                    <DropdownMenuItem className="hover:bg-cream-100 hover:text-forest-600">
                      <Star className="h-4 w-4 mr-2" />
                      Add to Favorites
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </>
            )}
          </>
        )}

        {/* App Launcher */}
        <AppLauncher
          activePersona={activePersona}
          setActivePersona={setActivePersona}
          setShowClientsModule={setShowClientsModule}
          setActiveTask={setActiveTask}
        />
      </div>
    </header>
  )
}


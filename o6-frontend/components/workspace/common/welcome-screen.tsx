"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { FileText, Filter, Users, ClipboardCheck, HeartPulse, GraduationCap } from "lucide-react"
import type { WorkspacePersona } from "../types"

interface WelcomeScreenProps {
  activePersona: WorkspacePersona
  onCreateItem: (type: string) => void
}

export function WelcomeScreen({ activePersona, onCreateItem }: WelcomeScreenProps) {
  return (
    <div className="h-full flex items-center justify-center">
      <div className="text-center max-w-lg">
        {activePersona === "recruiter" && <Users className="h-16 w-16 text-forest-600 mx-auto mb-4 opacity-80" />}
        {activePersona === "hr-generalist" && (
          <ClipboardCheck className="h-16 w-16 text-forest-600 mx-auto mb-4 opacity-80" />
        )}
        {activePersona === "benefits" && <HeartPulse className="h-16 w-16 text-forest-600 mx-auto mb-4 opacity-80" />}
        {activePersona === "learning" && (
          <GraduationCap className="h-16 w-16 text-forest-600 mx-auto mb-4 opacity-80" />
        )}

        <h3 className="text-2xl font-medium text-gray-800">
          {activePersona === "recruiter" && "Welcome to Recruiting Workspace"}
          {activePersona === "hr-generalist" && "Welcome to HR Management Workspace"}
          {activePersona === "benefits" && "Welcome to Benefits Administration Workspace"}
          {activePersona === "learning" && "Welcome to Learning & Development Workspace"}
        </h3>

        <p className="text-gray-600 mt-3 mb-6">
          {activePersona === "recruiter" &&
            "Manage your job descriptions, search strings, candidate profiles, and tasks all in one place."}
          {activePersona === "hr-generalist" &&
            "Manage employee records, policies, compliance, and HR analytics all in one place."}
          {activePersona === "benefits" &&
            "Manage benefits plans, enrollment campaigns, vendor contracts, and analytics all in one place."}
          {activePersona === "learning" &&
            "Manage training programs, learning paths, skills assessments, and analytics all in one place."}
        </p>

        <div className="mt-6 flex flex-wrap justify-center gap-3">
          {activePersona === "recruiter" && (
            <>
              <Button
                onClick={() => onCreateItem("job_description")}
                className="bg-forest-600 text-white hover:bg-forest-700"
              >
                <FileText className="h-4 w-4 mr-2" />
                Create Job Description
              </Button>
              <Button
                variant="outline"
                onClick={() => onCreateItem("boolean_search")}
                className="border-forest-600 text-forest-600 hover:bg-cream-100"
              >
                <Filter className="h-4 w-4 mr-2" />
                Create Boolean Search
              </Button>
              <Button
                variant="outline"
                onClick={() => onCreateItem("candidate_profile")}
                className="border-forest-600 text-forest-600 hover:bg-cream-100"
              >
                <Users className="h-4 w-4 mr-2" />
                Add Candidate Profile
              </Button>
            </>
          )}

          {activePersona === "hr-generalist" && (
            <>
              <Button
                onClick={() => onCreateItem("employee_record")}
                className="bg-forest-600 text-white hover:bg-forest-700"
              >
                <Users className="h-4 w-4 mr-2" />
                Create Employee Record
              </Button>
              <Button
                variant="outline"
                onClick={() => onCreateItem("policy_document")}
                className="border-forest-600 text-forest-600 hover:bg-cream-100"
              >
                <FileText className="h-4 w-4 mr-2" />
                Create Policy Document
              </Button>
              <Button
                variant="outline"
                onClick={() => onCreateItem("compliance_report")}
                className="border-forest-600 text-forest-600 hover:bg-cream-100"
              >
                <ClipboardCheck className="h-4 w-4 mr-2" />
                Create Compliance Report
              </Button>
            </>
          )}

          {activePersona === "benefits" && (
            <>
              <Button
                onClick={() => onCreateItem("benefits_plan")}
                className="bg-forest-600 text-white hover:bg-forest-700"
              >
                <HeartPulse className="h-4 w-4 mr-2" />
                Create Benefits Plan
              </Button>
              <Button
                variant="outline"
                onClick={() => onCreateItem("enrollment_campaign")}
                className="border-forest-600 text-forest-600 hover:bg-cream-100"
              >
                <Users className="h-4 w-4 mr-2" />
                Create Enrollment Campaign
              </Button>
              <Button
                variant="outline"
                onClick={() => onCreateItem("vendor_contract")}
                className="border-forest-600 text-forest-600 hover:bg-cream-100"
              >
                <FileText className="h-4 w-4 mr-2" />
                Add Vendor Contract
              </Button>
            </>
          )}

          {activePersona === "learning" && (
            <>
              <Button
                onClick={() => onCreateItem("training_program")}
                className="bg-forest-600 text-white hover:bg-forest-700"
              >
                <GraduationCap className="h-4 w-4 mr-2" />
                Create Training Program
              </Button>
              <Button
                variant="outline"
                onClick={() => onCreateItem("learning_path")}
                className="border-forest-600 text-forest-600 hover:bg-cream-100"
              >
                <FileText className="h-4 w-4 mr-2" />
                Create Learning Path
              </Button>
              <Button
                variant="outline"
                onClick={() => onCreateItem("skills_assessment")}
                className="border-forest-600 text-forest-600 hover:bg-cream-100"
              >
                <Users className="h-4 w-4 mr-2" />
                Create Skills Assessment
              </Button>
            </>
          )}
        </div>
      </div>
    </div>
  )
}


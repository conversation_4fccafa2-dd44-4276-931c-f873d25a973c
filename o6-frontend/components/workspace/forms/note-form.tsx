"use client"

import React, { useState, useEffect } from "react"
import { createNote } from "@/lib/data-service"
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from "@/components/ui/dialog"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Note, MeetingNote, PostingNote, SourcingNote, ScreeningNote } from "@/components/workspace/types"

interface NoteFormProps {
  isOpen: boolean
  onClose: () => void
  onSuccess: () => void
  taskId: string
  taskType?: string
}

export function NoteForm({ isOpen, onClose, onSuccess, taskId, taskType }: NoteFormProps) {
  // Set default note type based on task type if provided
  const getDefaultNoteType = (): "meeting" | "posting" | "sourcing" | "screening" => {
    if (taskType && ["meeting", "posting", "sourcing", "screening"].includes(taskType)) {
      return taskType as "meeting" | "posting" | "sourcing" | "screening"
    }
    return "meeting"
  }
  
  const [noteType, setNoteType] = useState<"meeting" | "posting" | "sourcing" | "screening">(getDefaultNoteType())
  const [formData, setFormData] = useState({
    title: "",
    // Meeting fields
    date: new Date().toISOString().split('T')[0],
    attendees: "",
    roleRequirements: "",
    budget: "",
    benefits: "",
    startDate: "",
    nextSteps: "",
    // Posting fields
    overview: "",
    company: "",
    location: "",
    salaryRange: "",
    experience: "",
    responsibilities: "",
    // Sourcing fields
    booleanString: "",
    analysis: "",
    expectedResults: "",
    // Screening fields
    candidateName: "",
    professionalSummary: "",
    screeningNotes: ""
  })
  const [isSubmitting, setIsSubmitting] = useState(false)

  // Reset note type when dialog opens based on task type
  useEffect(() => {
    if (isOpen) {
      setNoteType(getDefaultNoteType())
      resetFormData()
    }
  }, [isOpen, taskType])

  // Reset form data when note type changes
  useEffect(() => {
    resetFormData()
  }, [noteType])

  const resetFormData = () => {
    setFormData({
      title: "",
      // Meeting fields
      date: new Date().toISOString().split('T')[0],
      attendees: "",
      roleRequirements: "",
      budget: "",
      benefits: "",
      startDate: "",
      nextSteps: "",
      // Posting fields
      overview: "",
      company: "",
      location: "",
      salaryRange: "",
      experience: "",
      responsibilities: "",
      // Sourcing fields
      booleanString: "",
      analysis: "",
      expectedResults: "",
      // Screening fields
      candidateName: "",
      professionalSummary: "",
      screeningNotes: ""
    })
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    try {
      let noteData: Omit<Note, 'id' | 'lastEdited'>

      switch (noteType) {
        case "meeting":
          noteData = {
            taskId,
            type: "meeting",
            title: formData.title,
            date: new Date(formData.date),
            attendees: formData.attendees.split(',').map(a => a.trim()).filter(Boolean),
            roleRequirements: formData.roleRequirements.split(',').map(r => r.trim()).filter(Boolean),
            compensation: {
              budget: formData.budget,
              benefits: formData.benefits
            },
            timeline: {
              startDate: formData.startDate
            },
            nextSteps: formData.nextSteps.split(',').map(s => s.trim()).filter(Boolean)
          } as Omit<MeetingNote, 'id' | 'lastEdited'>
          break
        case "posting":
          noteData = {
            taskId,
            type: "posting",
            title: formData.title,
            overview: formData.overview,
            company: formData.company,
            location: formData.location,
            salaryRange: formData.salaryRange,
            experience: formData.experience,
            responsibilities: formData.responsibilities.split(',').map(r => r.trim()).filter(Boolean)
          } as Omit<PostingNote, 'id' | 'lastEdited'>
          break
        case "sourcing":
          noteData = {
            taskId,
            type: "sourcing",
            title: formData.title,
            booleanString: formData.booleanString,
            analysis: formData.analysis,
            expectedResults: formData.expectedResults
          } as Omit<SourcingNote, 'id' | 'lastEdited'>
          break
        case "screening":
          noteData = {
            taskId,
            type: "screening",
            candidateName: formData.title, // Using title field as candidate name
            professionalSummary: formData.professionalSummary,
            skills: [], // Will be empty for now, can be enhanced later
            screeningNotes: formData.screeningNotes
          } as Omit<ScreeningNote, 'id' | 'lastEdited'>
          break
        default:
          throw new Error("Invalid note type")
      }

      await createNote(noteData)

      // Reset form and close dialog
      resetFormData()
      onSuccess()
      onClose()
    } catch (error) {
      console.error("Failed to create note:", error)
    } finally {
      setIsSubmitting(false)
    }
  }

  const renderNoteTypeFields = () => {
    switch (noteType) {
      case "meeting":
        return (
          <>
            <div className="space-y-2">
              <Label htmlFor="date">Meeting Date</Label>
              <Input
                id="date"
                type="date"
                value={formData.date}
                onChange={(e) => setFormData({ ...formData, date: e.target.value })}
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="attendees">Attendees (comma-separated)</Label>
              <Input
                id="attendees"
                value={formData.attendees}
                onChange={(e) => setFormData({ ...formData, attendees: e.target.value })}
                placeholder="John Doe, Jane Smith"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="roleRequirements">Role Requirements (comma-separated)</Label>
              <Textarea
                id="roleRequirements"
                value={formData.roleRequirements}
                onChange={(e) => setFormData({ ...formData, roleRequirements: e.target.value })}
                placeholder="5+ years experience, Leadership skills"
                rows={2}
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="budget">Budget</Label>
                <Input
                  id="budget"
                  value={formData.budget}
                  onChange={(e) => setFormData({ ...formData, budget: e.target.value })}
                  placeholder="$80,000 - $120,000"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="benefits">Benefits</Label>
                <Input
                  id="benefits"
                  value={formData.benefits}
                  onChange={(e) => setFormData({ ...formData, benefits: e.target.value })}
                  placeholder="Health, 401k, PTO"
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="startDate">Start Date</Label>
              <Input
                id="startDate"
                value={formData.startDate}
                onChange={(e) => setFormData({ ...formData, startDate: e.target.value })}
                placeholder="ASAP or specific date"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="nextSteps">Next Steps (comma-separated)</Label>
              <Textarea
                id="nextSteps"
                value={formData.nextSteps}
                onChange={(e) => setFormData({ ...formData, nextSteps: e.target.value })}
                placeholder="Send job description, Schedule follow-up"
                rows={2}
              />
            </div>
          </>
        )
      case "posting":
        return (
          <>
            <div className="space-y-2">
              <Label htmlFor="overview">Job Overview</Label>
              <Textarea
                id="overview"
                value={formData.overview}
                onChange={(e) => setFormData({ ...formData, overview: e.target.value })}
                placeholder="Brief description of the role"
                rows={3}
                required
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="company">Company</Label>
                <Input
                  id="company"
                  value={formData.company}
                  onChange={(e) => setFormData({ ...formData, company: e.target.value })}
                  placeholder="Company name"
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="location">Location</Label>
                <Input
                  id="location"
                  value={formData.location}
                  onChange={(e) => setFormData({ ...formData, location: e.target.value })}
                  placeholder="City, State or Remote"
                  required
                />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="salaryRange">Salary Range</Label>
                <Input
                  id="salaryRange"
                  value={formData.salaryRange}
                  onChange={(e) => setFormData({ ...formData, salaryRange: e.target.value })}
                  placeholder="$80,000 - $120,000"
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="experience">Experience Required</Label>
                <Input
                  id="experience"
                  value={formData.experience}
                  onChange={(e) => setFormData({ ...formData, experience: e.target.value })}
                  placeholder="5+ years"
                  required
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="responsibilities">Key Responsibilities (comma-separated)</Label>
              <Textarea
                id="responsibilities"
                value={formData.responsibilities}
                onChange={(e) => setFormData({ ...formData, responsibilities: e.target.value })}
                placeholder="Lead team, Develop strategy, Manage projects"
                rows={3}
                required
              />
            </div>
          </>
        )
      case "sourcing":
        return (
          <>
            <div className="space-y-2">
              <Label htmlFor="booleanString">Boolean Search String</Label>
              <Textarea
                id="booleanString"
                value={formData.booleanString}
                onChange={(e) => setFormData({ ...formData, booleanString: e.target.value })}
                placeholder="(Java OR Python) AND (Senior OR Lead) AND -Junior"
                rows={3}
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="analysis">Search Analysis</Label>
              <Textarea
                id="analysis"
                value={formData.analysis}
                onChange={(e) => setFormData({ ...formData, analysis: e.target.value })}
                placeholder="Analysis of search strategy and approach"
                rows={3}
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="expectedResults">Expected Results</Label>
              <Textarea
                id="expectedResults"
                value={formData.expectedResults}
                onChange={(e) => setFormData({ ...formData, expectedResults: e.target.value })}
                placeholder="Number of candidates expected, timeline, etc."
                rows={2}
                required
              />
            </div>
          </>
        )
      case "screening":
        return (
          <>
            <div className="space-y-2">
              <Label htmlFor="candidateName">Candidate Name</Label>
              <Input
                id="candidateName"
                value={formData.candidateName}
                onChange={(e) => setFormData({ ...formData, candidateName: e.target.value })}
                placeholder="Enter candidate's name"
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="professionalSummary">Professional Summary</Label>
              <Textarea
                id="professionalSummary"
                value={formData.professionalSummary}
                onChange={(e) => setFormData({ ...formData, professionalSummary: e.target.value })}
                placeholder="Brief summary of candidate's background and experience"
                rows={4}
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="screeningNotes">Screening Notes</Label>
              <Textarea
                id="screeningNotes"
                value={formData.screeningNotes}
                onChange={(e) => setFormData({ ...formData, screeningNotes: e.target.value })}
                placeholder="Notes from the screening conversation"
                rows={4}
                required
              />
            </div>
          </>
        )
      default:
        return null
    }
  }

  const getNoteTypeInfo = () => {
    switch (noteType) {
      case "meeting":
        return {
          title: "Create Meeting Note",
          description: "Document meeting details, attendees, and action items."
        }
      case "posting":
        return {
          title: "Create Job Posting Note",
          description: "Define job requirements, company details, and responsibilities."
        }
      case "sourcing":
        return {
          title: "Create Sourcing Note", 
          description: "Document sourcing strategy, search strings, and expected results."
        }
      case "screening":
        return {
          title: "Create Screening Note",
          description: "Record candidate evaluation, skills assessment, and screening feedback."
        }
      default:
        return {
          title: "Create New Note",
          description: "Add a new note to document important information for this task."
        }
    }
  }

  const noteTypeInfo = getNoteTypeInfo()

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{noteTypeInfo.title}</DialogTitle>
          <DialogDescription>
            {noteTypeInfo.description}
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="noteType">Note Type</Label>
            <Select 
              value={noteType} 
              onValueChange={(value: "meeting" | "posting" | "sourcing" | "screening") => setNoteType(value)}
              disabled={!!(taskType && ["meeting", "posting", "sourcing", "screening"].includes(taskType))}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select note type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="meeting">Meeting Notes</SelectItem>
                <SelectItem value="posting">Job Posting</SelectItem>
                <SelectItem value="sourcing">Sourcing Strategy</SelectItem>
                <SelectItem value="screening">Candidate Screening</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="title">Title</Label>
            <Input
              id="title"
              value={formData.title}
              onChange={(e) => setFormData({ ...formData, title: e.target.value })}
              placeholder="Enter note title"
              required
            />
          </div>

          {renderNoteTypeFields()}

          <DialogFooter>
            <Button type="button" variant="outline" onClick={onClose} disabled={isSubmitting}>
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting} className="bg-forest-600 hover:bg-forest-700">
              {isSubmitting ? "Creating..." : "Create Note"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}

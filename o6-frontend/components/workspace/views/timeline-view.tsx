"use client"

import React, { useState, useEffect } from "react"
import { getPrograms, getObjectives, getTasks } from "@/lib/data-service"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Plus, Calendar, Filter, ChevronDown, CheckCircle2 } from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Progress } from "@/components/ui/progress"
import type { TaskType, WorkspacePersona, Program } from "../types"
import { TaskDetail } from "../timeline/task-detail"
import { ProgramForm } from "../program-form"
import { ObjectiveForm } from "../forms/objective-form"
import { TaskForm } from "../forms/task-form"
import { NoteForm } from "../forms/note-form"

interface TimelineViewProps {
  activePersona: WorkspacePersona
  onTaskSelect: (task: TaskType) => void
  isClientModule?: boolean
  resetTaskSelection?: () => void
}

export function TimelineView({ activePersona, onTaskSelect, isClientModule = false, resetTaskSelection }: TimelineViewProps) {
  const [currentQuarter, setCurrentQuarter] = useState<string>("current")
  const [viewMode, setViewMode] = useState<string>("standard")
  const [selectedTaskId, setSelectedTaskId] = useState<string | null>(null)
  const [selectedObjectiveId, setSelectedObjectiveId] = useState<string | null>(null)
  const [selectedProgramId, setSelectedProgramId] = useState<string | null>(null)
  const [programs, setPrograms] = useState<Program[]>([])
  const [objectives, setObjectives] = useState<any[]>([])
  const [tasks, setTasks] = useState<any[]>([])
  const [showProgramForm, setShowProgramForm] = useState(false)
  const [showObjectiveForm, setShowObjectiveForm] = useState(false)
  const [showTaskForm, setShowTaskForm] = useState(false)
  const [showNoteForm, setShowNoteForm] = useState(false)
  const [selectedProgramForObjective, setSelectedProgramForObjective] = useState<string | null>(null)
  const [selectedObjectiveForTask, setSelectedObjectiveForTask] = useState<string | null>(null)
  const [selectedTaskForNote, setSelectedTaskForNote] = useState<string | null>(null)

  useEffect(() => {
    (async () => {
      const fetchedPrograms = await getPrograms(activePersona)
      if (Array.isArray(fetchedPrograms)) setPrograms(fetchedPrograms)
    })()
  }, [activePersona])

  // Reset task selection when resetTaskSelection changes
  useEffect(() => {
    if (resetTaskSelection) {
      setSelectedTaskId(null)
      setSelectedObjectiveId(null)
      setSelectedProgramId(null)
    }
  }, [resetTaskSelection])

  // Helper function to ensure dates are properly converted
  const ensureDateObjects = (tasks: any[]) => {
    return tasks.map(task => ({
      ...task,
      dueDate: task.dueDate instanceof Date ? task.dueDate : new Date(task.dueDate)
    }));
  };

  useEffect(() => {
    // If a program is selected, fetch objectives and tasks for that program only
    if (selectedProgramId) {
      (async () => {
        const fetchedObjectives = await getObjectives(selectedProgramId, activePersona);
        if (Array.isArray(fetchedObjectives)) {
          setObjectives(fetchedObjectives);
          // Fetch all tasks for all objectives in parallel
          const allTasks: any[] = [];
          await Promise.all(
            fetchedObjectives.map(async (objective: any) => {
              const fetchedTasks = await getTasks(objective.id, activePersona);
              if (Array.isArray(fetchedTasks)) {
                allTasks.push(...fetchedTasks);
              }
            })
          );
          setTasks(ensureDateObjects(allTasks));
        }
      })();
    } else {
      // No program selected: fetch objectives and tasks for all visible programs
      (async () => {
        const allObjectives: any[] = [];
        const allTasks: any[] = [];
        await Promise.all(
          filteredPrograms.map(async (program) => {
            const fetchedObjectives = await getObjectives(program.id, activePersona);
            if (Array.isArray(fetchedObjectives)) {
              allObjectives.push(...fetchedObjectives);
              await Promise.all(
                fetchedObjectives.map(async (objective: any) => {
                  const fetchedTasks = await getTasks(objective.id, activePersona);
                  if (Array.isArray(fetchedTasks)) {
                    allTasks.push(...fetchedTasks);
                  }
                })
              );
            }
          })
        );
        setObjectives(allObjectives);
        setTasks(ensureDateObjects(allTasks));
      })();
    }
  }, [selectedProgramId, activePersona, currentQuarter, programs]);

  // Function to refresh programs after creating a new one
  const refreshPrograms = async () => {
    const fetchedPrograms = await getPrograms(activePersona)
    if (Array.isArray(fetchedPrograms)) setPrograms(fetchedPrograms)
  }

  // Function to refresh objectives after creating a new one
  const refreshObjectives = async () => {
    if (selectedProgramId) {
      const fetchedObjectives = await getObjectives(selectedProgramId, activePersona);
      if (Array.isArray(fetchedObjectives)) {
        setObjectives(fetchedObjectives);
      }
    } else {
      // No specific program selected, refresh all objectives for all programs
      const currentFilteredPrograms = filterProgramsByQuarter(programs, currentQuarter);
      const allObjectives: any[] = [];
      await Promise.all(
        currentFilteredPrograms.map(async (program) => {
          const fetchedObjectives = await getObjectives(program.id, activePersona);
          if (Array.isArray(fetchedObjectives)) {
            allObjectives.push(...fetchedObjectives);
          }
        })
      );
      setObjectives(allObjectives);
    }
  }

  // Function to refresh tasks after creating a new one
  const refreshTasks = async () => {
    if (selectedObjectiveForTask) {
      const fetchedTasks = await getTasks(selectedObjectiveForTask, activePersona);
      if (Array.isArray(fetchedTasks)) {
        const tasksWithDates = ensureDateObjects(fetchedTasks);
        setTasks(prevTasks => {
          const otherTasks = prevTasks.filter(t => t.objectiveId !== selectedObjectiveForTask);
          return [...otherTasks, ...tasksWithDates];
        });
      }
    }
  }

  // Filter programs based on selected quarter
  const filteredPrograms = filterProgramsByQuarter(programs, currentQuarter)

  // Get quarter options for dropdown
  const quarterOptions = getQuarterOptions()

  // Get selected task, objective, and program
  const selectedTask = selectedTaskId
    ? (isClientModule
        ? tasks.find(t => t.id === selectedTaskId)
        : tasks.find(t => t.id === selectedTaskId)) || null
    : null

  const selectedObjective = selectedObjectiveId ? objectives.find(o => o.id === selectedObjectiveId) || null : null

  const selectedProgram = selectedProgramId ? programs.find(p => p.id === selectedProgramId) || null : null

  // Handle task selection
  const handleTaskSelect = (taskId: string, objectiveId: string, programId: string) => {
    console.log("Task selected:", taskId, objectiveId, programId)
    setSelectedTaskId(taskId)
    setSelectedObjectiveId(objectiveId)
    setSelectedProgramId(programId)

    // Find the task and call the parent's onTaskSelect
    const task = isClientModule 
      ? tasks.find(t => t.id === taskId)
      : tasks.find(t => t.id === taskId)
      
    if (task) {
      onTaskSelect(task)
    }
  }

  // Handle navigation from breadcrumb
  const handleNavigate = (level: "timeline" | "program" | "objective" | "task") => {
    if (level === "timeline") {
      setSelectedTaskId(null)
      setSelectedObjectiveId(null)
      setSelectedProgramId(null)
      // Reset task selection in parent component as well
      if (resetTaskSelection) {
        resetTaskSelection()
      }
    } else if (level === "program") {
      setSelectedTaskId(null)
      setSelectedObjectiveId(null)
      // Keep the program selected
    } else if (level === "objective") {
      setSelectedTaskId(null)
      // Keep the objective and program selected
    }
    // For 'task', we keep all selections
  }

  // If we have a selected task, show the task detail view
  if (selectedTask && selectedObjective && selectedProgram) {
    return (
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <h1 className="text-2xl font-semibold text-gray-800">Timeline</h1>
          <Button 
            className="bg-forest-600 text-white hover:bg-forest-700"
            onClick={() => setShowProgramForm(true)}
          >
            <Plus className="h-4 w-4 mr-2" />
            New Program
          </Button>
        </div>

        <TaskDetail
          task={selectedTask}
          objective={selectedObjective}
          program={selectedProgram}
          onNavigate={handleNavigate}
        />
      </div>
    )
  }

  // Otherwise, show the timeline view
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-semibold text-gray-800">Timeline</h1>
        <Button 
          className="bg-forest-600 text-white hover:bg-forest-700"
          onClick={() => setShowProgramForm(true)}
        >
          <Plus className="h-4 w-4 mr-2" />
          New Program
        </Button>
      </div>

      <div className="flex justify-between items-center">
        <div className="flex items-center gap-2">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" className="border-gray-200 text-gray-700">
                {getQuarterDisplayName(currentQuarter)}
                <ChevronDown className="h-4 w-4 ml-2" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="start">
              <DropdownMenuRadioGroup value={currentQuarter} onValueChange={setCurrentQuarter}>
                {quarterOptions.map((option) => (
                  <DropdownMenuRadioItem key={option.value} value={option.value}>
                    {option.label}
                  </DropdownMenuRadioItem>
                ))}
              </DropdownMenuRadioGroup>
            </DropdownMenuContent>
          </DropdownMenu>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" className="border-gray-200 text-gray-700">
                <Filter className="h-4 w-4 mr-2" />
                View: {viewMode.charAt(0).toUpperCase() + viewMode.slice(1)}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="start">
              <DropdownMenuRadioGroup value={viewMode} onValueChange={setViewMode}>
                <DropdownMenuRadioItem value="collapsed">Collapsed</DropdownMenuRadioItem>
                <DropdownMenuRadioItem value="standard">Standard</DropdownMenuRadioItem>
                <DropdownMenuRadioItem value="detailed">Detailed</DropdownMenuRadioItem>
              </DropdownMenuRadioGroup>
            </DropdownMenuContent>
          </DropdownMenu>

          <Button variant="outline" className="border-gray-200 text-gray-700">
            <Calendar className="h-4 w-4 mr-2" />
            Calendar View
          </Button>
        </div>

        <div className="flex items-center gap-2">
          <Badge className="bg-blue-100 text-blue-800 border-blue-200">Active</Badge>
          <Badge className="bg-amber-100 text-amber-800 border-amber-200">Planning</Badge>
          <Badge className="bg-green-100 text-green-800 border-green-200">Completed</Badge>
        </div>
      </div>

      {filteredPrograms.length > 0 ? (
        <div className="space-y-4">
          {filteredPrograms.map((program) => (
            <div key={program.id} className="border border-gray-200 rounded-lg overflow-hidden bg-white">
              {/* Program Header */}
              <div className="flex items-center p-4 border-b border-gray-200">
                <Button
                  variant="ghost"
                  size="sm"
                  className="p-0 h-6 w-6 mr-2"
                  onClick={() => {
                    /* Toggle program expansion */
                  }}
                >
                  <ChevronDown className="h-4 w-4" />
                </Button>
                <div className="flex-1">
                  <div className="flex items-center gap-2">
                    <h2 className="text-lg font-medium">
                      {program.quarter} {program.year}: {program.title}
                    </h2>
                    <Badge
                      className={
                        program.status === "active"
                          ? "bg-blue-100 text-blue-800"
                          : program.status === "planning"
                            ? "bg-amber-100 text-amber-800"
                            : program.status === "completed"
                              ? "bg-green-100 text-green-800"
                              : "bg-gray-100 text-gray-800"
                      }
                    >
                      {program.status.charAt(0).toUpperCase() + program.status.slice(1)}
                    </Badge>
                  </div>
                  <div className="flex items-center text-sm text-gray-500 mt-1">
                    <span className="mr-4">Owner: {program.owner}</span>
                    <span>Objectives: {objectives.filter(o => o.programId === program.id).length}</span>
                  </div>
                </div>
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="ml-4"
                  onClick={() => {
                    setSelectedProgramForObjective(program.id)
                    setShowObjectiveForm(true)
                  }}
                >
                  <Plus className="h-4 w-4 mr-1" /> Add Objective
                </Button>
              </div>

              {/* Program Progress */}
              <div className="px-4 py-2 bg-gray-50 border-b border-gray-200">
                <div className="flex justify-between items-center text-sm mb-1">
                  <span className="text-gray-500">Progress:</span>
                  <span className="font-medium">{program.progress}%</span>
                </div>
                <Progress value={program.progress} className="h-2" />
              </div>

              {/* Objectives */}
              <div className="p-4">
                {objectives.filter(o => o.programId === program.id).map((objective) => {
                  const objectiveTasks = tasks.filter(t => t.objectiveId === objective.id);
                  const completedTasks = objectiveTasks.filter((t) => t.status === "completed").length;

                  return (
                    <div key={objective.id} className="mb-4 border border-gray-200 rounded-md overflow-hidden">
                      {/* Objective Header */}
                      <div className="flex items-center p-3 bg-gray-50 border-b border-gray-200">
                        <Button
                          variant="ghost"
                          size="sm"
                          className="p-0 h-6 w-6 mr-2"
                          onClick={() => {
                            /* Toggle objective expansion */
                          }}
                        >
                          <ChevronDown className="h-4 w-4" />
                        </Button>
                        <div className="flex-1">
                          <div className="flex items-center gap-2">
                            <h3 className="font-medium">{objective.title}</h3>
                            <Badge
                              className={
                                objective.status === "completed"
                                  ? "bg-green-100 text-green-800"
                                  : objective.status === "in_progress"
                                    ? "bg-blue-100 text-blue-800"
                                    : objective.status === "not_started"
                                      ? "bg-gray-100 text-gray-800"
                                      : "bg-red-100 text-red-800"
                              }
                            >
                              {objective.status
                                .split("_")
                                .map((word: string) => word.charAt(0).toUpperCase() + word.slice(1))
                                .join(" ")}
                            </Badge>
                          </div>
                          <div className="flex items-center text-sm text-gray-500 mt-1">
                            <span className="mr-4">Owner: {objective.owner}</span>
                            <span>
                              Tasks: {completedTasks}/{objectiveTasks.length}
                            </span>
                          </div>
                        </div>
                        <Button 
                          variant="outline" 
                          size="sm" 
                          className="ml-4"
                          onClick={() => {
                            setSelectedObjectiveForTask(objective.id)
                            setShowTaskForm(true)
                          }}
                        >
                          <Plus className="h-4 w-4 mr-1" /> Add Task
                        </Button>
                      </div>

                      {/* Objective Progress */}
                      <div className="px-3 py-2 border-b border-gray-200">
                        <div className="flex justify-between items-center text-sm mb-1">
                          <span className="text-gray-500">Progress:</span>
                          <span className="font-medium">{objective.progress}%</span>
                        </div>
                        <Progress value={objective.progress} className="h-2" />
                      </div>

                      {/* Tasks */}
                      <div className="p-3">
                        {objectiveTasks.map((task) => (
                          <div
                            key={task.id}
                            className="flex items-center p-2 hover:bg-gray-50 rounded-md cursor-pointer"
                            onClick={() => handleTaskSelect(task.id, objective.id, program.id)}
                          >
                            <div className="mr-3">
                              {task.status === "completed" ? (
                                <CheckCircle2 className="h-5 w-5 text-green-500" />
                              ) : (
                                <div
                                  className={`h-5 w-5 rounded-full border-2 ${
                                    task.status === "in_progress" ? "border-blue-500" : "border-gray-300"
                                  }`}
                                />
                              )}
                            </div>
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center justify-between">
                                <h4 className="font-medium text-gray-900 truncate">{task.title}</h4>
                                <div className="flex items-center ml-2">
                                  <Badge
                                    className={
                                      task.priority === "high"
                                        ? "bg-red-100 text-red-800"
                                        : task.priority === "medium"
                                          ? "bg-amber-100 text-amber-800"
                                          : "bg-green-100 text-green-800"
                                    }
                                  >
                                    {task.priority.charAt(0).toUpperCase() + task.priority.slice(1)}
                                  </Badge>
                                  <span className="ml-2 text-sm text-gray-500">
                                    {(() => {
                                      const date = task.dueDate instanceof Date ? task.dueDate : new Date(task.dueDate);
                                      return isNaN(date.getTime()) ? "Invalid date" : date.toLocaleDateString("en-US", {
                                        month: "short",
                                        day: "numeric",
                                      });
                                    })()}
                                  </span>
                                </div>
                              </div>
                              <p className="text-sm text-gray-500 truncate">{task.description}</p>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )
                })}
              </div>
            </div>
          ))}
        </div>
      ) : (
        <Card>
          <CardContent className="p-8 text-center">
            <h3 className="text-lg font-medium text-gray-800 mb-2">No programs found</h3>
            <p className="text-gray-500 mb-4">
              There are no programs for the selected quarter. Create a new program to get started.
            </p>
            <Button 
              className="bg-forest-600 text-white hover:bg-forest-700"
              onClick={() => setShowProgramForm(true)}
            >
              <Plus className="h-4 w-4 mr-2" />
              Create Program
            </Button>
          </CardContent>
        </Card>
      )}
      
      <ProgramForm
        open={showProgramForm}
        onOpenChange={setShowProgramForm}
        activePersona={activePersona}
        onProgramCreated={refreshPrograms}
      />
      
      <ObjectiveForm
        isOpen={showObjectiveForm}
        onClose={() => {
          setShowObjectiveForm(false)
          setSelectedProgramForObjective(null)
        }}
        onSuccess={refreshObjectives}
        programId={selectedProgramForObjective || ""}
        activePersona={activePersona}
      />
      
      <TaskForm
        isOpen={showTaskForm}
        onClose={() => {
          setShowTaskForm(false)
          setSelectedObjectiveForTask(null)
        }}
        onSuccess={refreshTasks}
        objectiveId={selectedObjectiveForTask || ""}
        activePersona={activePersona}
      />
      
      <NoteForm
        isOpen={showNoteForm}
        onClose={() => {
          setShowNoteForm(false)
          setSelectedTaskForNote(null)
        }}
        onSuccess={() => {
          // Notes don't need refresh in timeline view since they're not displayed here
          // But we could add a refresh callback if needed
        }}
        taskId={selectedTaskForNote || ""}
      />
    </div>
  )
}

// Helper functions
function filterProgramsByQuarter(programs: Program[], quarterFilter: string): Program[] {
  if (quarterFilter === "all") {
    return programs
  }

  const now = new Date()
  const currentYear = now.getFullYear()
  const currentQuarterNum = Math.floor(now.getMonth() / 3) + 1
  const currentQuarter = `Q${currentQuarterNum}` as "Q1" | "Q2" | "Q3" | "Q4"

  if (quarterFilter === "current") {
    return programs.filter((program) => program.quarter === currentQuarter && program.year === currentYear)
  }

  if (quarterFilter === "next") {
    let nextQuarterNum = currentQuarterNum + 1
    let nextQuarterYear = currentYear

    if (nextQuarterNum > 4) {
      nextQuarterNum = 1
      nextQuarterYear += 1
    }

    const nextQuarter = `Q${nextQuarterNum}` as "Q1" | "Q2" | "Q3" | "Q4"

    return programs.filter((program) => program.quarter === nextQuarter && program.year === nextQuarterYear)
  }

  // For specific quarter filters (e.g., "Q1-2023")
  if (quarterFilter.includes("-")) {
    const [quarter, year] = quarterFilter.split("-")
    return programs.filter((program) => program.quarter === quarter && program.year === Number.parseInt(year))
  }

  return programs
}

function getQuarterOptions() {
  const now = new Date()
  const currentYear = now.getFullYear()
  const currentQuarterNum = Math.floor(now.getMonth() / 3) + 1

  const options = [
    { value: "current", label: `Current Quarter (Q${currentQuarterNum} ${currentYear})` },
    { value: "next", label: `Next Quarter` },
    { value: "all", label: "All Quarters" },
  ]

  // Add specific quarters
  for (let i = 0; i < 4; i++) {
    let quarterNum = currentQuarterNum - i
    let year = currentYear

    if (quarterNum <= 0) {
      quarterNum += 4
      year -= 1
    }

    options.push({
      value: `Q${quarterNum}-${year}`,
      label: `Q${quarterNum} ${year}`,
    })
  }

  return options
}

function getQuarterDisplayName(quarterFilter: string) {
  const now = new Date()
  const currentYear = now.getFullYear()
  const currentQuarterNum = Math.floor(now.getMonth() / 3) + 1

  if (quarterFilter === "current") {
    return `Q${currentQuarterNum} ${currentYear}`
  }

  if (quarterFilter === "next") {
    let nextQuarterNum = currentQuarterNum + 1
    let nextQuarterYear = currentYear

    if (nextQuarterNum > 4) {
      nextQuarterNum = 1
      nextQuarterYear += 1
    }

    return `Q${nextQuarterNum} ${nextQuarterYear}`
  }

  if (quarterFilter === "all") {
    return "All Quarters"
  }

  // For specific quarter filters (e.g., "Q1-2023")
  if (quarterFilter.includes("-")) {
    const [quarter, year] = quarterFilter.split("-")
    return `${quarter} ${year}`
  }

  return quarterFilter
}


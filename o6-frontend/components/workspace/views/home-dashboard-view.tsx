import { <PERSON>, CardContent, CardDescription, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { BarChart3, Clock, FileText, Users } from "lucide-react"

export function HomeDashboardView() {
  return (
    <div className="container mx-auto p-6">
      <h1 className="text-2xl font-bold mb-6">Welcome to O6</h1>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">Total Employees</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <Users className="h-5 w-5 text-forest-600 mr-2" />
              <span className="text-2xl font-bold">248</span>
            </div>
            <p className="text-xs text-muted-foreground mt-1">+12 this month</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">Active Projects</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <FileText className="h-5 w-5 text-forest-600 mr-2" />
              <span className="text-2xl font-bold">18</span>
            </div>
            <p className="text-xs text-muted-foreground mt-1">3 due this week</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">Upcoming Reviews</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <Clock className="h-5 w-5 text-forest-600 mr-2" />
              <span className="text-2xl font-bold">7</span>
            </div>
            <p className="text-xs text-muted-foreground mt-1">Next in 3 days</p>
          </CardContent>
        </Card>
      </div>

      {/* Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle>Recent Activity</CardTitle>
              <CardDescription>Latest updates across your organization</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-start">
                  <div className="bg-forest-100 p-2 rounded-full mr-3">
                    <Users className="h-4 w-4 text-forest-600" />
                  </div>
                  <div>
                    <p className="font-medium">New employee onboarded</p>
                    <p className="text-sm text-muted-foreground">Sarah Johnson - Marketing Specialist</p>
                    <p className="text-xs text-muted-foreground mt-1">2 hours ago</p>
                  </div>
                </div>

                <div className="flex items-start">
                  <div className="bg-forest-100 p-2 rounded-full mr-3">
                    <FileText className="h-4 w-4 text-forest-600" />
                  </div>
                  <div>
                    <p className="font-medium">Performance review completed</p>
                    <p className="text-sm text-muted-foreground">John Smith - Software Engineer</p>
                    <p className="text-xs text-muted-foreground mt-1">Yesterday</p>
                  </div>
                </div>

                <div className="flex items-start">
                  <div className="bg-forest-100 p-2 rounded-full mr-3">
                    <BarChart3 className="h-4 w-4 text-forest-600" />
                  </div>
                  <div>
                    <p className="font-medium">Quarterly report published</p>
                    <p className="text-sm text-muted-foreground">HR Department</p>
                    <p className="text-xs text-muted-foreground mt-1">3 days ago</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Quick Actions */}
        <div>
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
              <CardDescription>Common tasks and shortcuts</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <button className="w-full text-left px-3 py-2 rounded-md hover:bg-forest-50 text-sm font-medium flex items-center">
                  <Users className="h-4 w-4 mr-2 text-forest-600" />
                  View employee directory
                </button>
                <button className="w-full text-left px-3 py-2 rounded-md hover:bg-forest-50 text-sm font-medium flex items-center">
                  <FileText className="h-4 w-4 mr-2 text-forest-600" />
                  Create new report
                </button>
                <button className="w-full text-left px-3 py-2 rounded-md hover:bg-forest-50 text-sm font-medium flex items-center">
                  <Clock className="h-4 w-4 mr-2 text-forest-600" />
                  Schedule meeting
                </button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}


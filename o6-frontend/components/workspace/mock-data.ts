import type { WorkspaceItem, TaskType, BlockType } from "./types"

export function getMockItems(): WorkspaceItem[] {
  return [
    {
      id: "1",
      title: "Senior Python Developer - Fintech",
      content: "We are seeking a highly skilled and motivated Python Developer...",
      tags: ["Python", "Fintech", "Senior"],
      createdAt: new Date("2023-03-15"),
      updatedAt: new Date("2023-03-18"),
      type: "job_description",
      status: "active",
      category: "Job Descriptions",
      versions: [
        {
          id: "v1",
          content: "Initial draft of Python Developer job description",
          createdAt: new Date("2023-03-15"),
        },
        {
          id: "v2",
          content: "Updated Python Developer job description with additional requirements",
          createdAt: new Date("2023-03-18"),
        },
      ],
    },
    {
      id: "2",
      title: "Boolean Search - Python Developers in Bay Area",
      content:
        '(python OR "python developer") AND (django OR flask) AND (senior OR sr OR lead) AND ("bay area" OR san francisco OR "san jose" OR oakland) -junior -intern',
      tags: ["Python", "Bay Area", "Boolean Search"],
      createdAt: new Date("2023-02-10"),
      updatedAt: new Date("2023-02-12"),
      type: "boolean_search",
      status: "active",
      category: "Search Strings",
      versions: [
        {
          id: "v1",
          content: "Initial boolean search string",
          createdAt: new Date("2023-02-10"),
        },
      ],
    },
    {
      id: "3",
      title: "Client Meeting - Acme Financial",
      content:
        "Meeting notes from discussion with Acme Financial about their Python Developer role requirements and team fit.",
      tags: ["Meeting Notes", "Client", "Acme Financial", "Full Kit"],
      createdAt: new Date("2023-03-05"),
      updatedAt: new Date("2023-03-05"),
      type: "meeting_notes",
      status: "completed",
      category: "Meetings",
      versions: [
        {
          id: "v1",
          content: "Notes from Acme Financial meeting",
          createdAt: new Date("2023-03-05"),
        },
      ],
    },
    {
      id: "4",
      title: "John Smith - Python Developer Profile",
      content: "Experience: 8 years Python, 5 years Django, 3 years Flask...",
      tags: ["Candidate", "Python", "Experienced"],
      createdAt: new Date("2023-03-20"),
      updatedAt: new Date("2023-03-22"),
      type: "candidate_profile",
      status: "active",
      category: "Candidates",
      versions: [
        {
          id: "v1",
          content: "Initial candidate profile",
          createdAt: new Date("2023-03-20"),
        },
      ],
    },
  ]
}

export function getMockTasks(): TaskType[] {
  return [
    {
      id: "t1",
      title: "Post Senior Python Developer job on Indeed",
      description: "Format the job description and post it on Indeed portal",
      dueDate: new Date("2023-04-05"),
      status: "pending",
      priority: "high",
      relatedItems: ["1"],
    },
    {
      id: "t2",
      title: "Send bulk mail to Python candidates",
      description: "Send job announcement to all Python developers in our database",
      dueDate: new Date("2023-04-07"),
      status: "pending",
      priority: "medium",
      relatedItems: ["1"],
    },
    {
      id: "t3",
      title: "Screen John Smith for Python role",
      description: "Conduct initial screening call with John Smith for the Senior Python Developer role",
      dueDate: new Date("2023-04-03"),
      status: "in_progress",
      priority: "high",
      relatedItems: ["1", "4"],
    },
    {
      id: "t4",
      title: "Follow up with Acme Financial",
      description: "Send follow-up email regarding our meeting about their developer needs",
      dueDate: new Date("2023-04-01"),
      status: "completed",
      priority: "medium",
      relatedItems: ["3"],
    },
  ]
}

export function getInitialBlocks(type?: string, item?: WorkspaceItem): BlockType[] {
  if (type === "job_description" || (!type && !item)) {
    return [
      {
        id: "block-1",
        type: "heading",
        content: item?.title || "Senior Python Developer - Fintech",
      },
      {
        id: "block-2",
        type: "subheading",
        content: "Overview",
      },
      {
        id: "block-3",
        type: "text",
        content:
          "We are seeking a highly skilled and motivated Senior Python Developer to join our dynamic Fintech team. The ideal candidate will have a strong background in software development with Python, a passion for financial technology, and the ability to solve complex problems.",
      },
      {
        id: "block-4",
        type: "job_details",
        content: JSON.stringify({
          company: "Acme Financial",
          location: "San Francisco, CA (Hybrid)",
          salary: "$150,000 - $180,000",
          experience: "5+ years",
        }),
      },
      {
        id: "block-5",
        type: "subheading",
        content: "Key Responsibilities",
      },
      {
        id: "block-6",
        type: "bullet",
        content: "Developing and maintaining Python-based applications for financial data processing and analysis.",
      },
      {
        id: "block-7",
        type: "bullet",
        content: "Writing clean, efficient, and reusable code following best practices in the fintech industry.",
      },
      {
        id: "block-8",
        type: "bullet",
        content:
          "Collaborating with cross-functional teams, including data scientists, product managers, and other developers.",
      },
    ]
  } else if (type === "boolean_search") {
    return [
      {
        id: "block-1",
        type: "heading",
        content: item?.title || "Boolean Search - Python Developers in Bay Area",
      },
      {
        id: "block-2",
        type: "boolean_search",
        content:
          item?.content ||
          '(python OR "python developer") AND (django OR flask) AND (senior OR sr OR lead) AND ("bay area" OR san francisco OR "san jose" OR oakland) -junior -intern',
      },
      {
        id: "block-3",
        type: "subheading",
        content: "Search Analysis",
      },
      {
        id: "block-4",
        type: "text",
        content:
          "This search string targets experienced Python developers in the Bay Area with experience in Django or Flask frameworks. It excludes junior positions and internships.",
      },
      {
        id: "block-5",
        type: "subheading",
        content: "Expected Results",
      },
      {
        id: "block-6",
        type: "text",
        content: "Estimated 200-300 matches on LinkedIn, 150-200 on Indeed, and 100-150 on Dice.",
      },
    ]
  } else if (type === "candidate_profile") {
    return [
      {
        id: "block-1",
        type: "heading",
        content: item?.title || "John Smith - Python Developer Profile",
      },
      {
        id: "block-2",
        type: "subheading",
        content: "Professional Summary",
      },
      {
        id: "block-3",
        type: "text",
        content:
          "Experienced Python Developer with 8 years of experience building web applications and data processing systems. Strong expertise in Django and Flask frameworks, with a background in fintech applications.",
      },
      {
        id: "block-4",
        type: "subheading",
        content: "Skills Assessment",
      },
      {
        id: "block-5",
        type: "candidate_rating",
        content: JSON.stringify({
          python: 9,
          django: 8,
          flask: 8,
          sql: 7,
          react: 6,
          aws: 7,
        }),
      },
      {
        id: "block-6",
        type: "subheading",
        content: "Screening Notes",
      },
      {
        id: "block-7",
        type: "text",
        content:
          "John demonstrates strong technical skills and has relevant experience in fintech. He seems particularly interested in data processing applications and has experience with financial APIs. Currently making $145k and looking for at least $160k.",
      },
    ]
  } else if (type === "meeting_notes") {
    return [
      {
        id: "block-1",
        type: "heading",
        content: item?.title || "Client Meeting - Acme Financial",
      },
      {
        id: "block-2",
        type: "subheading",
        content: "Meeting Details",
      },
      {
        id: "block-3",
        type: "text",
        content:
          "Date: March 5, 2023\nAttendees: Sarah Johnson (Acme Financial, CTO), Michael Chen (Acme Financial, Team Lead), Alex Rivera (Recruiter)",
      },
      {
        id: "block-4",
        type: "subheading",
        content: "Role Requirements (Full Kit)",
      },
      {
        id: "block-5",
        type: "bullet",
        content: "Senior Python Developer with 5+ years experience in financial applications",
      },
      {
        id: "block-6",
        type: "bullet",
        content: "Strong experience with Django and Flask frameworks required",
      },
      {
        id: "block-7",
        type: "bullet",
        content: "Knowledge of financial data processing and analysis is essential",
      },
      {
        id: "block-8",
        type: "bullet",
        content: "Team will be 5-7 developers, hybrid work environment (3 days in office)",
      },
      {
        id: "block-9",
        type: "subheading",
        content: "Compensation & Timeline",
      },
      {
        id: "block-10",
        type: "text",
        content:
          "Budget: $150,000 - $180,000 depending on experience\nBenefits: Standard package plus annual performance bonus\nStart date: Aiming to have someone onboard within 6 weeks",
      },
      {
        id: "block-11",
        type: "subheading",
        content: "Next Steps",
      },
      {
        id: "block-12",
        type: "bullet",
        content: "Create formal job description based on these requirements",
      },
      {
        id: "block-13",
        type: "bullet",
        content: "Send draft JD to Sarah for approval by Friday",
      },
      {
        id: "block-14",
        type: "bullet",
        content: "Begin sourcing candidates once JD is approved",
      },
    ]
  }

  // Default case for new items
  return [
    {
      id: "block-1",
      type: "heading",
      content:
        type === "job_description"
          ? "New Job Description"
          : type === "boolean_search"
            ? "New Boolean Search"
            : type === "candidate_profile"
              ? "New Candidate Profile"
              : type === "meeting_notes"
                ? "New Meeting Notes"
                : "New Item",
    },
  ]
}


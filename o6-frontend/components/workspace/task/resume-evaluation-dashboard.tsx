"use client"

import React, { useState, useEffect } from "react"

import { Upload, FileText, User, Star, TrendingUp, AlertCircle, CheckCircle, X, Building, Briefcase, ChevronUp, ChevronDown, ChevronsUpDown } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { getClients, getJobs } from "@/lib/data-service"
import type { Client, Job } from "@/components/workspace/types"

interface ResumeEvaluation {
  id: string
  task_id: string
  candidate_name: string
  resume_text: string
  job_description: string
  similarity_score: number | string  // Can be string from API
  evaluation_result: {
    candidate_name: string
    overall_score: number
    skills_match: {
      score: number
      present_skills: string[]
      missing_skills: string[]
      explanation: string
    }
    experience_relevance: {
      score: number
      explanation: string
    }
    recommendations: string[]
  }
  jury_flag: boolean | null
  jury_critic: string | null
  created_at: string
  updated_at: string
}

interface ResumeEvaluationDashboardProps {
  taskId: string
}

// Helper function to normalize evaluation data
const normalizeEvaluationData = (data: any): ResumeEvaluation[] => {
  console.log('Normalizing data:', data)
  
  if (Array.isArray(data)) {
    return data.map(item => normalizeEvaluationItem(item))
  }
  
  if (data.save_status?.data) {
    return data.save_status.data.map((item: any) => normalizeEvaluationItem(item))
  }
  
  if (data.data) {
    return data.data.map((item: any) => normalizeEvaluationItem(item))
  }
  
  return []
}

const normalizeEvaluationItem = (item: any): ResumeEvaluation => {
  // Ensure evaluation_result exists and has the right structure
  const evaluation_result = item.evaluation_result || {}
  
  return {
    ...item,
    id: String(item.id),
    task_id: String(item.task_id),
    similarity_score: item.similarity_score,
    evaluation_result: {
      candidate_name: evaluation_result.candidate_name || item.candidate_name || 'Unknown',
      overall_score: evaluation_result.overall_score || 0,
      skills_match: {
        score: evaluation_result.skills_match?.score || 0,
        present_skills: evaluation_result.skills_match?.present_skills || [],
        missing_skills: evaluation_result.skills_match?.missing_skills || [],
        explanation: evaluation_result.skills_match?.explanation || 'No explanation available'
      },
      experience_relevance: {
        score: evaluation_result.experience_relevance?.score || 0,
        explanation: evaluation_result.experience_relevance?.explanation || 'No explanation available'
      },
      recommendations: evaluation_result.recommendations || []
    }
  }
}

export default function ResumeEvaluationDashboard({ taskId }: ResumeEvaluationDashboardProps) {
  const [candidates, setCandidates] = useState<ResumeEvaluation[]>([])
  const [expandedRows, setExpandedRows] = useState<Set<string>>(new Set())
  const [dragActive, setDragActive] = useState(false)
  
  // State for client and job selection
  const [clients, setClients] = useState<Client[]>([])
  const [jobs, setJobs] = useState<Job[]>([])
  const [selectedClientId, setSelectedClientId] = useState<string>("")
  const [selectedJobId, setSelectedJobId] = useState<string>("")
  const [jobDescription, setJobDescription] = useState<string>("")
  const [loading, setLoading] = useState(false)
  const [uploading, setUploading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  
  // State for file upload
  const [uploadedFiles, setUploadedFiles] = useState<File[]>([])
  const inputRef = React.useRef<HTMLInputElement>(null)
  
  // State for sorting
  const [sortField, setSortField] = useState<string>('overall_score')
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc')

  // Sorting function
  const handleSort = (field: string) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc')
    } else {
      setSortField(field)
      setSortDirection('desc')
    }
  }

  // Get sorted candidates
  const getSortedCandidates = () => {
    return [...candidates].sort((a, b) => {
      let aValue: any
      let bValue: any

      switch (sortField) {
        case 'candidate_name':
          aValue = a.candidate_name.toLowerCase()
          bValue = b.candidate_name.toLowerCase()
          break
        case 'overall_score':
          aValue = a.evaluation_result?.overall_score || 0
          bValue = b.evaluation_result?.overall_score || 0
          break
        case 'skills_match':
          aValue = a.evaluation_result?.skills_match?.score || 0
          bValue = b.evaluation_result?.skills_match?.score || 0
          break
        case 'experience':
          aValue = a.evaluation_result?.experience_relevance?.score || 0
          bValue = b.evaluation_result?.experience_relevance?.score || 0
          break
        case 'similarity':
          aValue = parseFloat(String(a.similarity_score)) || 0
          bValue = parseFloat(String(b.similarity_score)) || 0
          break
        default:
          return 0
      }

      if (sortDirection === 'asc') {
        return aValue > bValue ? 1 : aValue < bValue ? -1 : 0
      } else {
        return aValue < bValue ? 1 : aValue > bValue ? -1 : 0
      }
    })
  }

  // Get sort icon for column headers
  const getSortIcon = (field: string) => {
    if (sortField !== field) {
      return <ChevronsUpDown className="h-4 w-4 text-gray-400" />
    }
    return sortDirection === 'asc' 
      ? <ChevronUp className="h-4 w-4 text-blue-600" />
      : <ChevronDown className="h-4 w-4 text-blue-600" />
  }

  // Debug: Log taskId to check if it's being passed correctly
  React.useEffect(() => {
    console.log('ResumeEvaluationDashboard taskId:', taskId)
    if (!taskId) {
      setError('Task ID is required but not provided')
    }
  }, [taskId])

  // Fetch resume evaluations on component mount
  useEffect(() => {
    const fetchEvaluations = async () => {
      try {
        setLoading(true)
        const response = await fetch(`/api/tasks/${taskId}/resume-evaluation`)
        if (!response.ok) {
          throw new Error('Failed to fetch resume evaluations')
        }
        const data = await response.json() as any
        console.log('Raw API response:', data)
        
        const evaluationsData = normalizeEvaluationData(data)
        console.log('Normalized evaluationsData:', evaluationsData)
        setCandidates(evaluationsData)
      } catch (err) {
        console.error('Error fetching resume evaluations:', err)
        setError('Failed to load resume evaluations')
      } finally {
        setLoading(false)
      }
    }

    if (taskId) {
      fetchEvaluations()
    }
  }, [taskId])

  // Fetch clients on component mount
  useEffect(() => {
    const fetchClients = async () => {
      try {
        setLoading(true)
        const clientsData = await getClients()
        setClients(clientsData)
      } catch (err) {
        console.error('Error fetching clients:', err)
        setError('Failed to load clients')
      } finally {
        setLoading(false)
      }
    }

    fetchClients()
  }, [])

  // Fetch jobs when client is selected
  useEffect(() => {
    const fetchJobs = async () => {
      if (!selectedClientId) {
        setJobs([])
        setSelectedJobId("")
        setJobDescription("")
        return
      }

      try {
        setLoading(true)
        const response = await fetch(`/api/jobs?clientId=${selectedClientId}`)
        if (!response.ok) {
          throw new Error('Failed to fetch jobs')
        }
        const jobsData = await response.json()
        setJobs(jobsData as Job[])
        setSelectedJobId("")
        setJobDescription("")
      } catch (err) {
        console.error('Error fetching jobs:', err)
        setError('Failed to load jobs for selected client')
      } finally {
        setLoading(false)
      }
    }

    fetchJobs()
  }, [selectedClientId])

  // Set job description when job is selected
  useEffect(() => {
    if (selectedJobId) {
      const selectedJob = jobs.find(job => job.id === selectedJobId)
      if (selectedJob) {
        setJobDescription(selectedJob.content || "")
      }
    } else {
      setJobDescription("")
    }
  }, [selectedJobId, jobs])

  const toggleRow = (id: string) => {
    const newExpanded = new Set(expandedRows)
    if (newExpanded.has(id)) {
      newExpanded.delete(id)
    } else {
      newExpanded.add(id)
    }
    setExpandedRows(newExpanded)
  }

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true)
    } else if (e.type === "dragleave") {
      // Check if we're actually leaving the drop zone
      const rect = e.currentTarget.getBoundingClientRect()
      const x = e.clientX
      const y = e.clientY
      
      if (x < rect.left || x > rect.right || y < rect.top || y > rect.bottom) {
        setDragActive(false)
      }
    }
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFiles(e.dataTransfer.files)
    }
  }

  const handleFiles = (event: React.ChangeEvent<HTMLInputElement> | FileList) => {
    const files = event instanceof FileList ? event : event.target.files
    if (files) {
      const fileArray = Array.from(files)
      setUploadedFiles(prev => [...prev, ...fileArray])
    }
  }

  const processResumeUploads = async () => {
    if (!taskId) {
      setError('Task ID is required but not provided')
      return
    }

    if (!jobDescription || uploadedFiles.length === 0) {
      setError('Please select a job and upload at least one resume file')
      return
    }

    setUploading(true)
    setError(null)

    try {
      console.log('Processing resumes for task:', taskId)
      
      const promises = uploadedFiles.map(async (file) => {
        const formData = new FormData()
        formData.append('job_description', jobDescription)
        formData.append('resume_file', file)

        console.log(`Making API call to: /api/tasks/${taskId}/resume-evaluation`)
        
        const response = await fetch(`/api/tasks/${taskId}/resume-evaluation`, {
          method: 'POST',
          body: formData,
        })

        if (!response.ok) {
          const errorText = await response.text().catch(() => 'Unknown error')
          console.error(`API Error for ${file.name}:`, response.status, errorText)
          throw new Error(`Failed to process ${file.name}: ${response.status} ${response.statusText}`)
        }

        return await response.json()
      })

      await Promise.all(promises)

      // Refresh the candidates list
      const response = await fetch(`/api/tasks/${taskId}/resume-evaluation`)
      if (response.ok) {
        const data = await response.json() as any
        console.log('Refresh API response:', data)
        
        const evaluationsData = normalizeEvaluationData(data)
        console.log('Refresh normalized evaluationsData:', evaluationsData)
        setCandidates(evaluationsData)
      }

      // Clear uploaded files
      setUploadedFiles([])
      
    } catch (err) {
      console.error('Error processing resumes:', err)
      setError('Failed to process some resume files. Please try again.')
    } finally {
      setUploading(false)
    }
  }

  const removeFile = (index: number) => {
    setUploadedFiles(prev => prev.filter((_, i) => i !== index))
  }

  const getScoreColor = (score: number) => {
    if (score >= 80) return "text-green-600"
    if (score >= 60) return "text-yellow-600"
    return "text-red-600"
  }

  const getScoreBadgeVariant = (score: number): "default" | "secondary" | "destructive" => {
    if (score >= 80) return "default"
    if (score >= 60) return "secondary"
    return "destructive"
  }

  // Don't render if taskId is not provided
  if (!taskId) {
    return (
      <div className="min-h-screen bg-gray-50 p-6">
        <div className="max-w-7xl mx-auto space-y-8">
          <div className="text-center space-y-2">
            <h1 className="text-3xl font-bold text-gray-900">Resume Evaluation</h1>
            <p className="text-red-600">Error: Task ID is required to use this component</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto space-y-8">
        {/* Header */}
        {/* <div className="text-center space-y-2">
          <h1 className="text-3xl font-bold text-gray-900">Candidate Screening</h1>
          <p className="text-gray-600">Upload resumes and analyze candidate fit against job requirements</p>
        </div> */}

        {/* Job Selection and Resume Upload Section */}
        <Card className="max-w-4xl mx-auto border border-gray-200 shadow-sm">
          {/* <CardHeader className="pb-3 bg-gradient-to-r from-slate-50 to-gray-50">
            <CardTitle className="flex items-center gap-2 text-lg">
              <div className="flex items-center gap-1">
                <Upload className="h-5 w-5 text-green-600" />
              </div>
              Job Setup & Resume Analysis
            </CardTitle>
            <CardDescription className="text-sm">
              Select client, job position, and upload resumes for evaluation
            </CardDescription>
          </CardHeader> */}
          
          <CardContent className="p-4 space-y-4">
            {/* Job Selection - Compact Layout */}
            <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-3 border border-blue-100">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                <div className="space-y-1">
                  <label className="text-xs font-medium text-gray-600">Client</label>
                  <Select value={selectedClientId} onValueChange={setSelectedClientId}>
                    <SelectTrigger className="h-9 bg-white text-sm">
                      <SelectValue placeholder="Select client..." />
                    </SelectTrigger>
                    <SelectContent>
                      {clients.map((client) => (
                        <SelectItem key={client.id} value={client.id}>
                          <div className="flex items-center gap-2">
                            <Building className="h-3 w-3" />
                            <span className="truncate">{client.name}</span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-1">
                  <label className="text-xs font-medium text-gray-600">Job Position</label>
                  <Select 
                    value={selectedJobId} 
                    onValueChange={setSelectedJobId}
                    disabled={!selectedClientId || loading}
                  >
                    <SelectTrigger className="h-9 bg-white text-sm">
                      <SelectValue placeholder={
                        !selectedClientId ? "Select client first..." : loading ? "Loading..." : "Select job..."
                      } />
                    </SelectTrigger>
                    <SelectContent>
                      {jobs.map((job) => (
                        <SelectItem key={job.id} value={job.id}>
                          <span className="truncate">{job.title}</span>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Compact Job Details */}
              {(() => {
                const selectedJob = jobs.find(job => job.id === selectedJobId)
                return selectedJob ? (
                  <div className="mt-3 bg-white rounded-md p-3 border border-blue-200">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium text-sm text-gray-900 truncate">{selectedJob.title}</h4>
                      <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200 text-xs">
                        <CheckCircle className="h-3 w-3 mr-1" />
                        Ready
                      </Badge>
                    </div>
                    
                    <div className="flex flex-wrap gap-2 mb-2">
                      {selectedJob.location && (
                        <span className="bg-blue-100 px-2 py-0.5 rounded-full text-xs">📍 {selectedJob.location}</span>
                      )}
                      {selectedJob.department && (
                        <span className="bg-purple-100 px-2 py-0.5 rounded-full text-xs">🏢 {selectedJob.department}</span>
                      )}
                      {selectedJob.experience && (
                        <span className="bg-orange-100 px-2 py-0.5 rounded-full text-xs">⏱️ {selectedJob.experience}</span>
                      )}
                      {selectedJob.salary && (
                        <span className="bg-green-100 px-2 py-0.5 rounded-full text-xs font-medium">💰 {selectedJob.salary}</span>
                      )}
                    </div>
                    
                    {jobDescription && (
                      <div className="max-h-16 p-2 bg-gray-50 rounded text-xs overflow-y-auto border">
                        {jobDescription.length > 150 ? `${jobDescription.substring(0, 150)}...` : jobDescription}
                      </div>
                    )}
                  </div>
                ) : null
              })()}

              {error && (
                <div className="mt-2 text-red-600 text-xs flex items-center gap-1">
                  <AlertCircle className="h-3 w-3" />
                  {error}
                </div>
              )}
            </div>

            {/* Resume Upload - Compact */}
            <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg p-3 border border-green-100">
              {/* Upload Drop Zone */}
              <div
                className={`relative border-2 border-dashed rounded-md p-4 text-center transition-colors cursor-pointer ${
                  dragActive ? "border-green-400 bg-green-100" : "border-green-300 hover:border-green-400 bg-white"
                }`}
                onDragEnter={handleDrag}
                onDragLeave={handleDrag}
                onDragOver={handleDrag}
                onDrop={handleDrop}
                onClick={() => inputRef.current?.click()}
              >
                <input
                  ref={inputRef}
                  type="file"
                  multiple
                  accept=".pdf,.doc,.docx"
                  onChange={handleFiles}
                  className="hidden"
                />
                
                <div className="space-y-2">
                  <Upload className="mx-auto h-6 w-6 text-green-600" />
                  <p className="text-sm font-medium text-gray-700">
                    {dragActive ? "Drop files here" : "Drop or click to upload resumes"}
                  </p>
                  <p className="text-xs text-gray-500">PDF, DOC, DOCX (max 10MB each)</p>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation()
                      inputRef.current?.click()
                    }}
                    className="mt-2 bg-white hover:bg-green-50 border-green-300 text-green-700"
                  >
                    Choose Files
                  </Button>
                </div>
              </div>

              {/* Compact File List */}
              {uploadedFiles.length > 0 && (
                <div className="mt-3">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-xs font-medium text-gray-600">Files ({uploadedFiles.length})</span>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setUploadedFiles([])}
                      className="h-6 text-xs text-red-600 hover:text-red-700 px-2"
                    >
                      Clear all
                    </Button>
                  </div>
                  <div className="max-h-20 overflow-y-auto space-y-1">
                    {uploadedFiles.map((file, index) => (
                      <div key={index} className="flex items-center justify-between bg-white p-2 rounded text-xs border border-green-200">
                        <div className="flex items-center gap-2 truncate flex-1">
                          <FileText className="h-3 w-3 text-green-600 flex-shrink-0" />
                          <span className="truncate">{file.name}</span>
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => removeFile(index)}
                          className="h-5 w-5 p-0 hover:bg-red-100 flex-shrink-0"
                        >
                          <X className="h-2 w-2" />
                        </Button>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>

            {/* Process Button */}
            {uploadedFiles.length > 0 && jobDescription && (
              <div className="mt-4 text-center">
                <Button 
                  onClick={processResumeUploads}
                  disabled={uploading}
                  className="bg-green-600 hover:bg-green-700 text-white"
                >
                  {uploading ? "Processing..." : `Process ${uploadedFiles.length} Resume${uploadedFiles.length > 1 ? 's' : ''}`}
                </Button>
              </div>
            )}
         
          </CardContent>
        </Card>

        {/* Candidates Table */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              Candidate Evaluations ({candidates.length})
            </CardTitle>
            <CardDescription>Click on any row to view detailed evaluation results</CardDescription>
          </CardHeader>
          <CardContent className="p-0">
            <Table className="table-fixed">
              <TableHeader>
                <TableRow>
                  <TableHead className="w-[280px]">
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-auto p-0 font-medium hover:bg-transparent"
                      onClick={() => handleSort('candidate_name')}
                    >
                      <span className="flex items-center gap-1">
                        Candidate
                        {getSortIcon('candidate_name')}
                      </span>
                    </Button>
                  </TableHead>
                  <TableHead className="w-[120px]">
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-auto p-0 font-medium hover:bg-transparent"
                      onClick={() => handleSort('overall_score')}
                    >
                      <span className="flex items-center gap-1">
                        Overall Score
                        {getSortIcon('overall_score')}
                      </span>
                    </Button>
                  </TableHead>
                  <TableHead className="w-[140px]">
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-auto p-0 font-medium hover:bg-transparent"
                      onClick={() => handleSort('skills_match')}
                    >
                      <span className="flex items-center gap-1">
                        Skills Match
                        {getSortIcon('skills_match')}
                      </span>
                    </Button>
                  </TableHead>
                  <TableHead className="w-[120px]">
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-auto p-0 font-medium hover:bg-transparent"
                      onClick={() => handleSort('experience')}
                    >
                      <span className="flex items-center gap-1">
                        Experience
                        {getSortIcon('experience')}
                      </span>
                    </Button>
                  </TableHead>
                  <TableHead className="w-[100px]">
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-auto p-0 font-medium hover:bg-transparent"
                      onClick={() => handleSort('similarity')}
                    >
                      <span className="flex items-center gap-1">
                        Similarity
                        {getSortIcon('similarity')}
                      </span>
                    </Button>
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {getSortedCandidates().map((candidate) => (
                  <React.Fragment key={candidate.id}>
                    <TableRow
                      className="cursor-pointer hover:bg-gray-50 transition-colors"
                      onClick={() => toggleRow(candidate.id)}
                    >
                      <TableCell className="font-medium">
                        <div className="space-y-1">
                          <div className="font-semibold">{candidate.candidate_name}</div>
                          <div className="text-sm text-gray-500">ID: {candidate.id.slice(0, 8)}...</div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          {candidate.evaluation_result?.overall_score ? (
                            <>
                              <Badge variant={getScoreBadgeVariant(candidate.evaluation_result.overall_score)}>
                                {candidate.evaluation_result.overall_score}%
                              </Badge>
                              <Star className={`h-4 w-4 ${getScoreColor(candidate.evaluation_result.overall_score)}`} />
                            </>
                          ) : (
                            <span className="text-gray-400">N/A</span>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          {candidate.evaluation_result?.skills_match?.score ? (
                            <>
                              <span className={`font-medium ${getScoreColor(candidate.evaluation_result.skills_match.score)}`}>
                                {candidate.evaluation_result.skills_match.score}%
                              </span>
                              <Progress value={candidate.evaluation_result.skills_match.score} className="w-16 h-2" />
                            </>
                          ) : (
                            <span className="text-gray-400">N/A</span>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          {candidate.evaluation_result?.experience_relevance?.score ? (
                            <>
                              <span className={`font-medium ${getScoreColor(candidate.evaluation_result.experience_relevance.score)}`}>
                                {candidate.evaluation_result.experience_relevance.score}%
                              </span>
                              <TrendingUp className={`h-4 w-4 ${getScoreColor(candidate.evaluation_result.experience_relevance.score)}`} />
                            </>
                          ) : (
                            <span className="text-gray-400">N/A</span>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">{(parseFloat(String(candidate.similarity_score)) * 100).toFixed(1)}%</Badge>
                      </TableCell>
                    </TableRow>
                    {expandedRows.has(candidate.id) && (
                      <TableRow>
                        <TableCell colSpan={5} className="p-0 bg-gray-50">
                          <div className="p-6 space-y-6 border-t">
                            {/* Skills Analysis */}
                            <div className="grid md:grid-cols-2 gap-6">
                              <Card>
                                <CardHeader className="pb-3">
                                  <CardTitle className="text-lg flex items-center gap-2">
                                    <CheckCircle className="h-5 w-5 text-green-600" />
                                    Present Skills
                                  </CardTitle>
                                </CardHeader>
                                <CardContent>
                                  <div className="flex flex-wrap gap-2">
                                    {candidate.evaluation_result?.skills_match?.present_skills?.map((skill: string, index: number) => (
                                      <Badge key={index} variant="default" className="bg-green-100 text-green-800">
                                        {skill}
                                      </Badge>
                                    )) || <span className="text-gray-400">No skills data available</span>}
                                  </div>
                                </CardContent>
                              </Card>

                              <Card>
                                <CardHeader className="pb-3">
                                  <CardTitle className="text-lg flex items-center gap-2">
                                    <X className="h-5 w-5 text-red-600" />
                                    Missing Skills
                                  </CardTitle>
                                </CardHeader>
                                <CardContent>
                                  <div className="flex flex-wrap gap-2">
                                    {candidate.evaluation_result?.skills_match?.missing_skills?.map((skill: string, index: number) => (
                                      <Badge key={index} variant="destructive" className="bg-red-100 text-red-800">
                                        {skill}
                                      </Badge>
                                    )) || <span className="text-gray-400">No missing skills data available</span>}
                                  </div>
                                </CardContent>
                              </Card>
                            </div>

                            {/* Experience Analysis */}
                            <Card>
                              <CardHeader className="pb-3">
                                <CardTitle className="text-lg flex items-center gap-2">
                                  <TrendingUp className="h-5 w-5 text-blue-600" />
                                  Experience Relevance
                                </CardTitle>
                              </CardHeader>
                              <CardContent>
                                <div className="space-y-3">
                                  <div className="flex items-center gap-4">
                                    <span className="font-semibold">Score:</span>
                                    {candidate.evaluation_result?.experience_relevance?.score ? (
                                      <Badge variant={getScoreBadgeVariant(candidate.evaluation_result.experience_relevance.score)}>
                                        {candidate.evaluation_result.experience_relevance.score}%
                                      </Badge>
                                    ) : (
                                      <span className="text-gray-400">N/A</span>
                                    )}
                                  </div>
                                  <p className="text-gray-700">
                                    {candidate.evaluation_result?.experience_relevance?.explanation || "No explanation available"}
                                  </p>
                                </div>
                              </CardContent>
                            </Card>

                            {/* Recommendations */}
                            <Card>
                              <CardHeader className="pb-3">
                                <CardTitle className="text-lg flex items-center gap-2">
                                  <AlertCircle className="h-5 w-5 text-orange-600" />
                                  Recommendations
                                </CardTitle>
                              </CardHeader>
                              <CardContent>
                                <ScrollArea className="h-32">
                                  <ul className="space-y-2">
                                    {candidate.evaluation_result?.recommendations?.map((rec: string, index: number) => (
                                      <li key={index} className="flex items-start gap-2">
                                        <div className="w-2 h-2 bg-orange-400 rounded-full mt-2 flex-shrink-0" />
                                        <span className="text-gray-700">{rec}</span>
                                      </li>
                                    )) || <span className="text-gray-400">No recommendations available</span>}
                                  </ul>
                                </ScrollArea>
                              </CardContent>
                            </Card>
                          </div>
                        </TableCell>
                      </TableRow>
                    )}
                  </React.Fragment>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

"use client"

import React, { useState, useEffect } from "react"
import { Edit, Save, X } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { getNotes, createNote } from "@/lib/data-service"
import { Badge } from "@/components/ui/badge"
import { NoteForm } from "../forms/note-form"
import ResumeEvaluationDashboard from "./resume-evaluation-dashboard"
import type { TaskType, Note, MeetingNote, PostingNote, SourcingNote, ScreeningNote } from "../types"

// Note editor components for each note type
const MeetingNoteEditor = ({ note, onSave }: { note: MeetingNote, onSave: (updatedNote: MeetingNote) => void }) => {
  const [editedNote, setEditedNote] = useState<MeetingNote>(note)

  const handleChange = (field: keyof MeetingNote, value: any) => {
    setEditedNote(prev => ({ ...prev, [field]: value }))
  }

  const handleArrayChange = (field: keyof MeetingNote, index: number, value: string) => {
    const newArray = [...(editedNote[field] as string[])]
    newArray[index] = value
    setEditedNote(prev => ({ ...prev, [field]: newArray }))
  }

  return (
    <div className="space-y-4">
      <div>
        <h3 className="text-sm font-medium mb-1">Title</h3>
        <input 
          className="w-full p-2 border rounded-md" 
          value={editedNote.title} 
          onChange={e => handleChange('title', e.target.value)} 
        />
      </div>
      
      <div>
        <h3 className="text-sm font-medium mb-1">Date</h3>
        <input 
          type="date" 
          className="p-2 border rounded-md" 
          value={editedNote.date.toISOString().split('T')[0]} 
          onChange={e => handleChange('date', new Date(e.target.value))} 
        />
      </div>
      
      <div>
        <h3 className="text-sm font-medium mb-1">Attendees</h3>
        {editedNote.attendees.map((attendee, index) => (
          <div key={index} className="flex items-center mb-2">
            <input 
              className="flex-1 p-2 border rounded-md" 
              value={attendee} 
              onChange={e => handleArrayChange('attendees', index, e.target.value)} 
            />
            {index === editedNote.attendees.length - 1 && (
              <Button 
                variant="ghost" 
                size="sm" 
                onClick={() => handleChange('attendees', [...editedNote.attendees, ''])}
                className="ml-2"
              >
                + Add
              </Button>
            )}
          </div>
        ))}
      </div>
      
      <div>
        <h3 className="text-sm font-medium mb-1">Role Requirements</h3>
        {editedNote.roleRequirements.map((req, index) => (
          <div key={index} className="flex items-center mb-2">
            <input 
              className="flex-1 p-2 border rounded-md" 
              value={req} 
              onChange={e => handleArrayChange('roleRequirements', index, e.target.value)} 
            />
            {index === editedNote.roleRequirements.length - 1 && (
              <Button 
                variant="ghost" 
                size="sm" 
                onClick={() => handleChange('roleRequirements', [...editedNote.roleRequirements, ''])}
                className="ml-2"
              >
                + Add
              </Button>
            )}
          </div>
        ))}
      </div>
      
      <div>
        <h3 className="text-sm font-medium mb-1">Compensation</h3>
        <div className="pl-4 space-y-2">
          <div>
            <label className="text-xs text-gray-500">Budget</label>
            <input 
              className="w-full p-2 border rounded-md" 
              value={editedNote.compensation.budget} 
              onChange={e => setEditedNote(prev => ({
                ...prev,
                compensation: { ...prev.compensation, budget: e.target.value }
              }))} 
            />
          </div>
          <div>
            <label className="text-xs text-gray-500">Benefits</label>
            <input 
              className="w-full p-2 border rounded-md" 
              value={editedNote.compensation.benefits} 
              onChange={e => setEditedNote(prev => ({
                ...prev,
                compensation: { ...prev.compensation, benefits: e.target.value }
              }))} 
            />
          </div>
        </div>
      </div>
      
      <div>
        <h3 className="text-sm font-medium mb-1">Timeline</h3>
        <div className="pl-4">
          <label className="text-xs text-gray-500">Start Date</label>
          <input 
            className="w-full p-2 border rounded-md" 
            value={editedNote.timeline.startDate} 
            onChange={e => setEditedNote(prev => ({
              ...prev,
              timeline: { ...prev.timeline, startDate: e.target.value }
            }))} 
          />
        </div>
      </div>
      
      <div>
        <h3 className="text-sm font-medium mb-1">Next Steps</h3>
        {editedNote.nextSteps.map((step, index) => (
          <div key={index} className="flex items-center mb-2">
            <input 
              className="flex-1 p-2 border rounded-md" 
              value={step} 
              onChange={e => handleArrayChange('nextSteps', index, e.target.value)} 
            />
            {index === editedNote.nextSteps.length - 1 && (
              <Button 
                variant="ghost" 
                size="sm" 
                onClick={() => handleChange('nextSteps', [...editedNote.nextSteps, ''])}
                className="ml-2"
              >
                + Add
              </Button>
            )}
          </div>
        ))}
      </div>
      
      <div className="flex justify-end">
        <Button onClick={() => onSave(editedNote)}>Save Changes</Button>
      </div>
    </div>
  )
}

const PostingNoteEditor = ({ note, onSave }: { note: PostingNote, onSave: (updatedNote: PostingNote) => void }) => {
  const [editedNote, setEditedNote] = useState<PostingNote>(note)

  const handleChange = (field: keyof PostingNote, value: any) => {
    setEditedNote(prev => ({ ...prev, [field]: value }))
  }

  const handleArrayChange = (index: number, value: string) => {
    const newResponsibilities = [...editedNote.responsibilities]
    newResponsibilities[index] = value
    setEditedNote(prev => ({ ...prev, responsibilities: newResponsibilities }))
  }

  return (
    <div className="space-y-4">
      <div>
        <h3 className="text-sm font-medium mb-1">Title</h3>
        <input 
          className="w-full p-2 border rounded-md" 
          value={editedNote.title} 
          onChange={e => handleChange('title', e.target.value)} 
        />
      </div>
      
      <div>
        <h3 className="text-sm font-medium mb-1">Overview</h3>
        <textarea 
          className="w-full p-2 border rounded-md min-h-[100px]" 
          value={editedNote.overview} 
          onChange={e => handleChange('overview', e.target.value)} 
        />
      </div>
      
      <div className="grid grid-cols-2 gap-4">
        <div>
          <h3 className="text-sm font-medium mb-1">Company</h3>
          <input 
            className="w-full p-2 border rounded-md" 
            value={editedNote.company} 
            onChange={e => handleChange('company', e.target.value)} 
          />
        </div>
        <div>
          <h3 className="text-sm font-medium mb-1">Location</h3>
          <input 
            className="w-full p-2 border rounded-md" 
            value={editedNote.location} 
            onChange={e => handleChange('location', e.target.value)} 
          />
        </div>
      </div>
      
      <div className="grid grid-cols-2 gap-4">
        <div>
          <h3 className="text-sm font-medium mb-1">Salary Range</h3>
          <input 
            className="w-full p-2 border rounded-md" 
            value={editedNote.salaryRange} 
            onChange={e => handleChange('salaryRange', e.target.value)} 
          />
        </div>
        <div>
          <h3 className="text-sm font-medium mb-1">Experience</h3>
          <input 
            className="w-full p-2 border rounded-md" 
            value={editedNote.experience} 
            onChange={e => handleChange('experience', e.target.value)} 
          />
        </div>
      </div>
      
      <div>
        <h3 className="text-sm font-medium mb-1">Responsibilities</h3>
        {editedNote.responsibilities.map((resp, index) => (
          <div key={index} className="flex items-center mb-2">
            <input 
              className="flex-1 p-2 border rounded-md" 
              value={resp} 
              onChange={e => handleArrayChange(index, e.target.value)} 
            />
            {index === editedNote.responsibilities.length - 1 && (
              <Button 
                variant="ghost" 
                size="sm" 
                onClick={() => handleChange('responsibilities', [...editedNote.responsibilities, ''])}
                className="ml-2"
              >
                + Add
              </Button>
            )}
          </div>
        ))}
      </div>
      
      <div className="flex justify-end">
        <Button onClick={() => onSave(editedNote)}>Save Changes</Button>
      </div>
    </div>
  )
}

const SourcingNoteEditor = ({ note, onSave }: { note: SourcingNote, onSave: (updatedNote: SourcingNote) => void }) => {
  const [editedNote, setEditedNote] = useState<SourcingNote>(note)

  const handleChange = (field: keyof SourcingNote, value: string) => {
    setEditedNote(prev => ({ ...prev, [field]: value }))
  }

  return (
    <div className="space-y-4">
      <div>
        <h3 className="text-sm font-medium mb-1">Title</h3>
        <input 
          className="w-full p-2 border rounded-md" 
          value={editedNote.title} 
          onChange={e => handleChange('title', e.target.value)} 
        />
      </div>
      
      <div>
        <h3 className="text-sm font-medium mb-1">Boolean Search String</h3>
        <textarea 
          className="w-full p-2 border rounded-md font-mono text-sm min-h-[80px]" 
          value={editedNote.booleanString} 
          onChange={e => handleChange('booleanString', e.target.value)} 
        />
      </div>
      
      <div>
        <h3 className="text-sm font-medium mb-1">Analysis</h3>
        <textarea 
          className="w-full p-2 border rounded-md min-h-[100px]" 
          value={editedNote.analysis} 
          onChange={e => handleChange('analysis', e.target.value)} 
        />
      </div>
      
      <div>
        <h3 className="text-sm font-medium mb-1">Expected Results</h3>
        <textarea 
          className="w-full p-2 border rounded-md min-h-[80px]" 
          value={editedNote.expectedResults} 
          onChange={e => handleChange('expectedResults', e.target.value)} 
        />
      </div>
      
      <div className="flex justify-end">
        <Button onClick={() => onSave(editedNote)}>Save Changes</Button>
      </div>
    </div>
  )
}

const ScreeningNoteEditor = ({ note, onSave }: { note: ScreeningNote, onSave: (updatedNote: ScreeningNote) => void }) => {
  const [editedNote, setEditedNote] = useState<ScreeningNote>(note)

  const handleChange = (field: keyof ScreeningNote, value: any) => {
    setEditedNote(prev => ({ ...prev, [field]: value }))
  }

  const handleSkillChange = (index: number, field: 'name' | 'rating', value: any) => {
    const newSkills = [...editedNote.skills]
    newSkills[index] = { ...newSkills[index], [field]: field === 'rating' ? Number(value) : value }
    setEditedNote(prev => ({ ...prev, skills: newSkills }))
  }

  return (
    <div className="space-y-4">
      <div>
        <h3 className="text-sm font-medium mb-1">Candidate Name</h3>
        <input 
          className="w-full p-2 border rounded-md" 
          value={editedNote.candidateName} 
          onChange={e => handleChange('candidateName', e.target.value)} 
        />
      </div>
      
      <div>
        <h3 className="text-sm font-medium mb-1">Professional Summary</h3>
        <textarea 
          className="w-full p-2 border rounded-md min-h-[100px]" 
          value={editedNote.professionalSummary} 
          onChange={e => handleChange('professionalSummary', e.target.value)} 
        />
      </div>
      
      <div>
        <h3 className="text-sm font-medium mb-1">Skills</h3>
        {editedNote.skills.map((skill, index) => (
          <div key={index} className="flex items-center mb-2 gap-2">
            <input 
              className="flex-1 p-2 border rounded-md" 
              placeholder="Skill name"
              value={skill.name} 
              onChange={e => handleSkillChange(index, 'name', e.target.value)} 
            />
            <select 
              className="p-2 border rounded-md" 
              value={skill.rating}
              onChange={e => handleSkillChange(index, 'rating', e.target.value)}
            >
              {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map(rating => (
                <option key={rating} value={rating}>{rating}</option>
              ))}
            </select>
            {index === editedNote.skills.length - 1 && (
              <Button 
                variant="ghost" 
                size="sm" 
                onClick={() => handleChange('skills', [...editedNote.skills, { name: '', rating: 5 }])}
              >
                + Add
              </Button>
            )}
          </div>
        ))}
      </div>
      
      <div>
        <h3 className="text-sm font-medium mb-1">Screening Notes</h3>
        <textarea 
          className="w-full p-2 border rounded-md min-h-[150px]" 
          value={editedNote.screeningNotes} 
          onChange={e => handleChange('screeningNotes', e.target.value)} 
        />
      </div>
      
      <div className="flex justify-end">
        <Button onClick={() => onSave(editedNote)}>Save Changes</Button>
      </div>
    </div>
  )
}

// Note viewers for each note type
const MeetingNoteViewer = ({ note }: { note: MeetingNote }) => (
  <div className="space-y-4">
    <div>
      <h3 className="text-sm font-medium text-gray-500">Meeting Date</h3>
      <p>{note.date.toLocaleDateString()}</p>
    </div>
    
    <div>
      <h3 className="text-sm font-medium text-gray-500">Attendees</h3>
      <ul className="list-disc pl-5">
        {note.attendees.map((attendee, index) => (
          <li key={index}>{attendee}</li>
        ))}
      </ul>
    </div>
    
    <div>
      <h3 className="text-sm font-medium text-gray-500">Role Requirements</h3>
      <ul className="list-disc pl-5">
        {note.roleRequirements.map((req, index) => (
          <li key={index}>{req}</li>
        ))}
      </ul>
    </div>
    
    <div>
      <h3 className="text-sm font-medium text-gray-500">Compensation</h3>
      <div className="pl-4">
        <div>
          <span className="text-xs text-gray-500">Budget:</span> {note.compensation.budget}
        </div>
        <div>
          <span className="text-xs text-gray-500">Benefits:</span> {note.compensation.benefits}
        </div>
      </div>
    </div>
    
    <div>
      <h3 className="text-sm font-medium text-gray-500">Timeline</h3>
      <div className="pl-4">
        <div>
          <span className="text-xs text-gray-500">Start Date:</span> {note.timeline.startDate}
        </div>
      </div>
    </div>
    
    <div>
      <h3 className="text-sm font-medium text-gray-500">Next Steps</h3>
      <ul className="list-disc pl-5">
        {note.nextSteps.map((step, index) => (
          <li key={index}>{step}</li>
        ))}
      </ul>
    </div>
  </div>
)

const PostingNoteViewer = ({ note }: { note: PostingNote }) => (
  <div className="space-y-4">
    <div>
      <h3 className="text-sm font-medium text-gray-500">Overview</h3>
      <p className="whitespace-pre-wrap">{note.overview}</p>
    </div>
    
    <div className="grid grid-cols-2 gap-4">
      <div>
        <h3 className="text-sm font-medium text-gray-500">Company</h3>
        <p>{note.company}</p>
      </div>
      <div>
        <h3 className="text-sm font-medium text-gray-500">Location</h3>
        <p>{note.location}</p>
      </div>
    </div>
    
    <div className="grid grid-cols-2 gap-4">
      <div>
        <h3 className="text-sm font-medium text-gray-500">Salary Range</h3>
        <p>{note.salaryRange}</p>
      </div>
      <div>
        <h3 className="text-sm font-medium text-gray-500">Experience</h3>
        <p>{note.experience}</p>
      </div>
    </div>
    
    <div>
      <h3 className="text-sm font-medium text-gray-500">Responsibilities</h3>
      <ul className="list-disc pl-5">
        {note.responsibilities.map((resp, index) => (
          <li key={index}>{resp}</li>
        ))}
      </ul>
    </div>
  </div>
)

const SourcingNoteViewer = ({ note }: { note: SourcingNote }) => (
  <div className="space-y-4">
    <div>
      <h3 className="text-sm font-medium text-gray-500">Boolean Search String</h3>
      <div className="bg-slate-50 p-3 rounded-md font-mono text-sm break-words">
        {note.booleanString}
      </div>
    </div>
    
    <div>
      <h3 className="text-sm font-medium text-gray-500">Analysis</h3>
      <p className="whitespace-pre-wrap">{note.analysis}</p>
    </div>
    
    <div>
      <h3 className="text-sm font-medium text-gray-500">Expected Results</h3>
      <p className="whitespace-pre-wrap">{note.expectedResults}</p>
    </div>
  </div>
)

const ScreeningNoteViewer = ({ note }: { note: ScreeningNote }) => (
  <div className="space-y-4">
    <div>
      <h3 className="text-sm font-medium text-gray-500">Professional Summary</h3>
      <p className="whitespace-pre-wrap">{note.professionalSummary}</p>
    </div>
    
    <div>
      <h3 className="text-sm font-medium text-gray-500">Skills</h3>
      <div className="grid grid-cols-2 gap-2">
        {note.skills.map((skill, index) => (
          <div key={index} className="flex justify-between items-center bg-slate-50 p-2 rounded-md">
            <span>{skill.name}</span>
            <Badge variant="secondary">{skill.rating}/10</Badge>
          </div>
        ))}
      </div>
    </div>
    
    <div>
      <h3 className="text-sm font-medium text-gray-500">Screening Notes</h3>
      <p className="whitespace-pre-wrap">{note.screeningNotes}</p>
    </div>
  </div>
)

interface TaskDetailViewProps {
  task: TaskType
  onClose?: () => void
}

export function TaskDetailView(props: TaskDetailViewProps) {
  const [isEditing, setIsEditing] = useState(false)
  const [note, setNote] = useState<any>(null)
  const [showNoteForm, setShowNoteForm] = useState(false)

  useEffect(() => {
    if (!props.task) return;
    (async () => {
      const fetchedNote = await getNotes(props.task.id)
      setNote(fetchedNote)
    })()
  }, [props.task?.id])

  // Format the task type for display
  const taskTypeFormatted = props.task.type ? props.task.type.charAt(0).toUpperCase() + props.task.type.slice(1) : "Other"

  // Get the appropriate badge color based on status
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "completed":
        return <Badge className="bg-green-500">Completed</Badge>
      case "in_progress":
        return <Badge className="bg-blue-500">In Progress</Badge>
      case "pending":
        return <Badge className="bg-orange-400">Pending</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  // Get the appropriate badge color based on priority
  const getPriorityBadge = (priority: string) => {
    switch (priority) {
      case "high":
        return <Badge className="bg-red-500">High</Badge>
      case "medium":
        return <Badge className="bg-yellow-500">Medium</Badge>
      case "low":
        return <Badge className="bg-green-500">Low</Badge>
      default:
        return <Badge variant="outline">{priority}</Badge>
    }
  }

  // Handle saving note changes
  const handleSaveNote = (updatedNote: Note) => {
    setNote(updatedNote)
    setIsEditing(false)
    // Here you would typically save the note to your API
    console.log("Saving updated note:", updatedNote)
  }

  // Handle creating a new note - now opens dialog
  const handleCreateNote = () => {
    if (!props.task.type || !["meeting", "posting", "sourcing", "screening"].includes(props.task.type)) return
    setShowNoteForm(true)
  }

  // Handle successful note creation from dialog
  const handleNoteCreated = async () => {
    // Refresh the note data after creation
    try {
      const fetchedNote = await getNotes(props.task.id)
      setNote(fetchedNote)
    } catch (error) {
      console.error("Failed to fetch updated note:", error)
    }
  }

  return (
    <div className="space-y-6">
      {/* Back navigation */}
      {props.onClose && (
        <div className="flex items-center gap-2">
          <Button 
            variant="ghost" 
            size="sm" 
            onClick={props.onClose}
            className="text-gray-600 hover:text-gray-900"
          >
            ← Back to Timeline
          </Button>
        </div>
      )}
      
      {/* Task overview card */}
      <Card>
        <CardHeader className="pb-2">
          <div className="flex justify-between items-start">
            <div>
              <CardTitle className="text-xl">{props.task.title}</CardTitle>
              <div className="text-sm text-gray-500 mt-1">Task ID: {props.task.id}</div>
            </div>
            <Badge variant="outline" className="ml-2">{taskTypeFormatted}</Badge>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <p className="text-gray-700">{props.task.description}</p>
            
            <div className="grid grid-cols-2 gap-4">
              <div>
                <h3 className="text-sm font-medium text-gray-500">Due Date</h3>
                <p>{new Date(props.task.dueDate).toLocaleDateString()}</p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-500">Assigned To</h3>
                <p>{props.task.assignedTo}</p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-500">Status</h3>
                <div>{getStatusBadge(props.task.status)}</div>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-500">Priority</h3>
                <div>{getPriorityBadge(props.task.priority)}</div>
              </div>
            </div>
            
            <div className="flex gap-2 pt-2">
              <Button 
                className="bg-forest-600 text-white hover:bg-forest-700"
                disabled={props.task.status === "completed"}
              >
                {props.task.status === "completed" ? "Completed" : "Complete Task"}
              </Button>
              <Button variant="outline" className="border-gray-200 text-gray-700">
                Edit Task
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>


      {/* Resume Evaluation Dashboard - shown only for candidate screening tasks */}
      {props.task.type === "screening" && (
        <ResumeEvaluationDashboard taskId={props.task.id} />
      )}

      {/* Create Note button - shown when no note exists and task has a supported type */}
      {!note && props.task.type && ["meeting", "posting", "sourcing", "screening"].includes(props.task.type) && (
        <Card>
          <CardContent className="py-6">
            <div className="text-center space-y-4">
              <div>
                <h3 className="text-lg font-medium text-gray-900">No notes yet</h3>
                <p className="text-sm text-gray-500">Create a note to track detailed information for this {props.task.type} task.</p>
              </div>
              <Button 
                onClick={handleCreateNote}
                className="bg-forest-600 text-white hover:bg-forest-700"
              >
                Create Note
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Note card */}
      {note && (
        <Card>
          <CardHeader className="pb-3 flex flex-row items-center justify-between">
            <div>
              <CardTitle className="text-lg">
                {note.type === "screening" ? "Candidate" : "Task"} Notes
              </CardTitle>
              <p className="text-sm text-gray-500">Last edited: {new Date(note.lastEdited).toLocaleDateString()}</p>
            </div>
            {isEditing ? (
              <div className="flex space-x-2">
                <Button 
                  variant="ghost" 
                  size="icon" 
                  className="h-8 w-8"
                  onClick={() => setIsEditing(false)}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            ) : (
              <Button 
                variant="ghost" 
                size="icon" 
                className="h-8 w-8"
                onClick={() => setIsEditing(true)}
              >
                <Edit className="h-4 w-4" />
              </Button>
            )}
          </CardHeader>
          <CardContent>
            {isEditing ? (
              /* Note Editors based on note type */
              <>
                {note.type === "meeting" && (
                  <MeetingNoteEditor note={note as MeetingNote} onSave={handleSaveNote} />
                )}
                {note.type === "posting" && (
                  <PostingNoteEditor note={note as PostingNote} onSave={handleSaveNote} />
                )}
                {note.type === "sourcing" && (
                  <SourcingNoteEditor note={note as SourcingNote} onSave={handleSaveNote} />
                )}
                {note.type === "screening" && (
                  <ScreeningNoteEditor note={note as ScreeningNote} onSave={handleSaveNote} />
                )}
              </>
            ) : (
              /* Note Viewers based on note type */
              <>
                {note.type === "meeting" && <MeetingNoteViewer note={note as MeetingNote} />}
                {note.type === "posting" && <PostingNoteViewer note={note as PostingNote} />}
                {note.type === "sourcing" && <SourcingNoteViewer note={note as SourcingNote} />}
                {note.type === "screening" && <ScreeningNoteViewer note={note as ScreeningNote} />}
              </>
            )}
          </CardContent>
        </Card>
      )}

      {/* Note Form Dialog */}
      <NoteForm
        isOpen={showNoteForm}
        onClose={() => setShowNoteForm(false)}
        onSuccess={handleNoteCreated}
        taskId={props.task.id}
        taskType={props.task.type}
      />
    </div>
  )
}
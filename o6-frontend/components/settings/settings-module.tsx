"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs"
import { OrganizationSettings } from "./organization-settings"
import { UserManagement } from "./user-management"
import { RoleManagement } from "./role-management"
import { SecuritySettings } from "./security-settings"

export function SettingsModule() {
  const [activeTab, setActiveTab] = useState("organization")

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-xl font-semibold text-gray-800">Settings</h1>
        <p className="text-gray-600">Manage your organization, users, roles, and security settings.</p>
      </div>

      <Tabs defaultValue="organization" value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="bg-cream-100 p-1">
          <TabsTrigger
            value="organization"
            className="data-[state=active]:bg-white data-[state=active]:text-forest-600 data-[state=active]:shadow-sm text-gray-600 rounded-md"
          >
            Organization
          </TabsTrigger>
          <TabsTrigger
            value="users"
            className="data-[state=active]:bg-white data-[state=active]:text-forest-600 data-[state=active]:shadow-sm text-gray-600 rounded-md"
          >
            Users
          </TabsTrigger>
          <TabsTrigger
            value="roles"
            className="data-[state=active]:bg-white data-[state=active]:text-forest-600 data-[state=active]:shadow-sm text-gray-600 rounded-md"
          >
            Roles
          </TabsTrigger>
          <TabsTrigger
            value="security"
            className="data-[state=active]:bg-white data-[state=active]:text-forest-600 data-[state=active]:shadow-sm text-gray-600 rounded-md"
          >
            Security
          </TabsTrigger>
        </TabsList>

        <TabsContent value="organization">
          <OrganizationSettings />
        </TabsContent>

        <TabsContent value="users">
          <UserManagement />
        </TabsContent>

        <TabsContent value="roles">
          <RoleManagement />
        </TabsContent>

        <TabsContent value="security">
          <SecuritySettings />
        </TabsContent>
      </Tabs>
    </div>
  )
}


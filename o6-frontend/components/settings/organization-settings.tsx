"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Building, MapPin, Globe, Phone, Mail, Users } from "lucide-react"

export function OrganizationSettings() {
  const [organization, setOrganization] = useState({
    name: "Acme Corporation",
    industry: "Technology",
    size: "51-200",
    description: "A leading technology company specializing in innovative solutions for businesses.",
    website: "https://acme.example.com",
    phone: "+****************",
    email: "<EMAIL>",
    address: "123 Main St, San Francisco, CA 94105",
  })

  const [isEditing, setIsEditing] = useState(false)
  const [formData, setFormData] = useState(organization)

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    setOrganization(formData)
    setIsEditing(false)
  }

  return (
    <div className="space-y-6">
      <Card className="border-gray-200/50 shadow-sm">
        <CardHeader className="flex flex-row items-start justify-between">
          <div>
            <CardTitle>Organization Details</CardTitle>
            <CardDescription>View and update your organization's basic information.</CardDescription>
          </div>
          {!isEditing && (
            <Button
              variant="outline"
              className="border-gray-200 text-gray-700 hover:border-forest-600 hover:text-forest-600"
              onClick={() => setIsEditing(true)}
            >
              Edit Details
            </Button>
          )}
        </CardHeader>
        <CardContent>
          {isEditing ? (
            <form onSubmit={handleSubmit}>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Organization Name</Label>
                  <Input id="name" name="name" value={formData.name} onChange={handleChange} required />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="industry">Industry</Label>
                  <select
                    id="industry"
                    name="industry"
                    value={formData.industry}
                    onChange={handleChange}
                    className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                    required
                  >
                    <option value="technology">Technology</option>
                    <option value="healthcare">Healthcare</option>
                    <option value="finance">Finance</option>
                    <option value="education">Education</option>
                    <option value="manufacturing">Manufacturing</option>
                    <option value="retail">Retail</option>
                    <option value="other">Other</option>
                  </select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="size">Organization Size</Label>
                  <select
                    id="size"
                    name="size"
                    value={formData.size}
                    onChange={handleChange}
                    className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                    required
                  >
                    <option value="1-10">1-10 employees</option>
                    <option value="11-50">11-50 employees</option>
                    <option value="51-200">51-200 employees</option>
                    <option value="201-500">201-500 employees</option>
                    <option value="501-1000">501-1000 employees</option>
                    <option value="1000+">1000+ employees</option>
                  </select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="website">Website</Label>
                  <Input id="website" name="website" value={formData.website} onChange={handleChange} />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="phone">Phone</Label>
                  <Input id="phone" name="phone" value={formData.phone} onChange={handleChange} />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="email">Email</Label>
                  <Input id="email" name="email" type="email" value={formData.email} onChange={handleChange} />
                </div>
                <div className="space-y-2 md:col-span-2">
                  <Label htmlFor="address">Address</Label>
                  <Input id="address" name="address" value={formData.address} onChange={handleChange} />
                </div>
                <div className="space-y-2 md:col-span-2">
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    name="description"
                    value={formData.description}
                    onChange={handleChange}
                    rows={4}
                  />
                </div>
              </div>
              <div className="flex justify-end mt-4 space-x-2">
                <Button
                  type="button"
                  variant="outline"
                  className="border-gray-200 text-gray-700"
                  onClick={() => setIsEditing(false)}
                >
                  Cancel
                </Button>
                <Button type="submit" className="bg-forest-600 hover:bg-forest-700 text-white">
                  Save Changes
                </Button>
              </div>
            </form>
          ) : (
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-start">
                  <Building className="h-5 w-5 text-forest-600 mr-2 mt-0.5" />
                  <div>
                    <p className="text-sm font-medium text-gray-500">Organization Name</p>
                    <p>{organization.name}</p>
                  </div>
                </div>
                <div className="flex items-start">
                  <Users className="h-5 w-5 text-forest-600 mr-2 mt-0.5" />
                  <div>
                    <p className="text-sm font-medium text-gray-500">Size</p>
                    <p>{organization.size} employees</p>
                  </div>
                </div>
                <div className="flex items-start">
                  <Globe className="h-5 w-5 text-forest-600 mr-2 mt-0.5" />
                  <div>
                    <p className="text-sm font-medium text-gray-500">Website</p>
                    <p>{organization.website}</p>
                  </div>
                </div>
                <div className="flex items-start">
                  <Phone className="h-5 w-5 text-forest-600 mr-2 mt-0.5" />
                  <div>
                    <p className="text-sm font-medium text-gray-500">Phone</p>
                    <p>{organization.phone}</p>
                  </div>
                </div>
                <div className="flex items-start">
                  <Mail className="h-5 w-5 text-forest-600 mr-2 mt-0.5" />
                  <div>
                    <p className="text-sm font-medium text-gray-500">Email</p>
                    <p>{organization.email}</p>
                  </div>
                </div>
                <div className="flex items-start">
                  <MapPin className="h-5 w-5 text-forest-600 mr-2 mt-0.5" />
                  <div>
                    <p className="text-sm font-medium text-gray-500">Address</p>
                    <p>{organization.address}</p>
                  </div>
                </div>
              </div>
              <div className="pt-2">
                <p className="text-sm font-medium text-gray-500">Description</p>
                <p className="mt-1">{organization.description}</p>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      <Card className="border-gray-200/50 shadow-sm">
        <CardHeader>
          <CardTitle>Billing Information</CardTitle>
          <CardDescription>Manage your subscription and billing details.</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="bg-cream-50 p-4 rounded-lg border border-gray-200/50">
              <div className="flex justify-between items-center">
                <div>
                  <p className="font-medium">Professional Plan</p>
                  <p className="text-sm text-gray-600">$99/month, billed annually</p>
                </div>
                <Button variant="outline" className="border-gray-200 text-gray-700">
                  Manage Subscription
                </Button>
              </div>
            </div>
            <p className="text-sm text-gray-600">
              Your next billing date is <span className="font-medium">January 1, 2024</span>.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}


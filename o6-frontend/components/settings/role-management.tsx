"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Switch } from "@/components/ui/switch"
import { Ta<PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Plus, Shield, Info } from "lucide-react"

// Define role and permission types
interface Role {
  id: string
  name: string
  description: string
  isSystem: boolean
  permissions: Record<string, Record<string, boolean>>
}

export function RoleManagement() {
  // Mock roles data
  const [roles, setRoles] = useState<Role[]>([
    {
      id: "1",
      name: "Owner",
      description: "Full access to all resources and settings",
      isSystem: true,
      permissions: {
        organization: {
          view: true,
          edit: true,
          delete: true,
        },
        users: {
          view: true,
          create: true,
          edit: true,
          delete: true,
        },
        roles: {
          view: true,
          create: true,
          edit: true,
          delete: true,
        },
        hcm: {
          view: true,
          create: true,
          edit: true,
          delete: true,
        },
        crm: {
          view: true,
          create: true,
          edit: true,
          delete: true,
        },
      },
    },
    {
      id: "2",
      name: "Admin",
      description: "Administrative access to most resources",
      isSystem: true,
      permissions: {
        organization: {
          view: true,
          edit: true,
          delete: false,
        },
        users: {
          view: true,
          create: true,
          edit: true,
          delete: false,
        },
        roles: {
          view: true,
          create: false,
          edit: false,
          delete: false,
        },
        hcm: {
          view: true,
          create: true,
          edit: true,
          delete: true,
        },
        crm: {
          view: true,
          create: true,
          edit: true,
          delete: true,
        },
      },
    },
    {
      id: "3",
      name: "HR Manager",
      description: "Manages HR-related resources",
      isSystem: false,
      permissions: {
        organization: {
          view: true,
          edit: false,
          delete: false,
        },
        users: {
          view: true,
          create: false,
          edit: false,
          delete: false,
        },
        roles: {
          view: false,
          create: false,
          edit: false,
          delete: false,
        },
        hcm: {
          view: true,
          create: true,
          edit: true,
          delete: false,
        },
        crm: {
          view: true,
          create: false,
          edit: false,
          delete: false,
        },
      },
    },
    {
      id: "4",
      name: "Recruiter",
      description: "Manages recruiting and client relationships",
      isSystem: false,
      permissions: {
        organization: {
          view: true,
          edit: false,
          delete: false,
        },
        users: {
          view: false,
          create: false,
          edit: false,
          delete: false,
        },
        roles: {
          view: false,
          create: false,
          edit: false,
          delete: false,
        },
        hcm: {
          view: true,
          create: true,
          edit: true,
          delete: false,
        },
        crm: {
          view: true,
          create: true,
          edit: true,
          delete: false,
        },
      },
    },
  ])

  const [selectedRole, setSelectedRole] = useState<Role | null>(null)
  const [isAddRoleOpen, setIsAddRoleOpen] = useState(false)
  const [newRole, setNewRole] = useState({
    name: "",
    description: "",
  })

  const handleAddRole = () => {
    const newRoleData: Role = {
      id: `${roles.length + 1}`,
      name: newRole.name,
      description: newRole.description,
      isSystem: false,
      permissions: {
        organization: {
          view: true,
          edit: false,
          delete: false,
        },
        users: {
          view: false,
          create: false,
          edit: false,
          delete: false,
        },
        roles: {
          view: false,
          create: false,
          edit: false,
          delete: false,
        },
        hcm: {
          view: true,
          create: false,
          edit: false,
          delete: false,
        },
        crm: {
          view: false,
          create: false,
          edit: false,
          delete: false,
        },
      },
    }

    setRoles([...roles, newRoleData])
    setNewRole({ name: "", description: "" })
    setIsAddRoleOpen(false)
    setSelectedRole(newRoleData)
  }

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="md:col-span-1">
          <Card className="border-gray-200/50 shadow-sm h-full">
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle>Roles</CardTitle>
                <CardDescription>Manage roles and permissions</CardDescription>
              </div>
              <Dialog open={isAddRoleOpen} onOpenChange={setIsAddRoleOpen}>
                <DialogTrigger asChild>
                  <Button size="sm" className="bg-forest-600 hover:bg-forest-700 text-white">
                    <Plus className="h-4 w-4 mr-1" />
                    Add Role
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Create New Role</DialogTitle>
                    <DialogDescription>Define a new role with custom permissions.</DialogDescription>
                  </DialogHeader>
                  <div className="space-y-4 py-4">
                    <div className="space-y-2">
                      <Label htmlFor="name">Role Name</Label>
                      <Input
                        id="name"
                        value={newRole.name}
                        onChange={(e) => setNewRole({ ...newRole, name: e.target.value })}
                        placeholder="Enter role name"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="description">Description</Label>
                      <Input
                        id="description"
                        value={newRole.description}
                        onChange={(e) => setNewRole({ ...newRole, description: e.target.value })}
                        placeholder="Enter role description"
                      />
                    </div>
                  </div>
                  <DialogFooter>
                    <Button
                      variant="outline"
                      onClick={() => setIsAddRoleOpen(false)}
                      className="border-gray-200 text-gray-700"
                    >
                      Cancel
                    </Button>
                    <Button onClick={handleAddRole} className="bg-forest-600 hover:bg-forest-700 text-white">
                      Create Role
                    </Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {roles.map((role) => (
                  <div
                    key={role.id}
                    className={`p-3 rounded-lg cursor-pointer ${
                      selectedRole?.id === role.id
                        ? "bg-forest-100 border border-forest-200"
                        : "hover:bg-cream-100 border border-transparent"
                    }`}
                    onClick={() => setSelectedRole(role)}
                  >
                    <div className="flex items-center">
                      <Shield className={`h-4 w-4 mr-2 ${role.isSystem ? "text-forest-600" : "text-gray-500"}`} />
                      <div className="flex-1">
                        <div className="font-medium">{role.name}</div>
                        <div className="text-xs text-gray-500">{role.description}</div>
                      </div>
                      {role.isSystem && (
                        <div className="text-xs bg-gray-100 text-gray-700 px-2 py-0.5 rounded">System</div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="md:col-span-2">
          {selectedRole ? (
            <Card className="border-gray-200/50 shadow-sm">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="flex items-center">
                      {selectedRole.name}
                      {selectedRole.isSystem && (
                        <div className="ml-2 text-xs bg-gray-100 text-gray-700 px-2 py-0.5 rounded flex items-center">
                          <Info className="h-3 w-3 mr-1" />
                          System Role
                        </div>
                      )}
                    </CardTitle>
                    <CardDescription>{selectedRole.description}</CardDescription>
                  </div>
                  {!selectedRole.isSystem && (
                    <Button variant="outline" className="border-gray-200 text-gray-700">
                      Edit Role
                    </Button>
                  )}
                </div>
              </CardHeader>
              <CardContent>
                <Tabs defaultValue="permissions">
                  <TabsList className="mb-4">
                    <TabsTrigger value="permissions">Permissions</TabsTrigger>
                    <TabsTrigger value="users">Users with this role</TabsTrigger>
                  </TabsList>
                  <TabsContent value="permissions">
                    <div className="space-y-6">
                      {Object.entries(selectedRole.permissions).map(([category, permissions]) => (
                        <div key={category} className="space-y-2">
                          <h3 className="font-medium capitalize">{category}</h3>
                          <div className="bg-cream-50 p-4 rounded-lg border border-gray-200/50">
                            <div className="grid grid-cols-2 gap-4">
                              {Object.entries(permissions).map(([permission, value]) => (
                                <div key={`${category}-${permission}`} className="flex items-center justify-between">
                                  <div className="flex items-center">
                                    <span className="capitalize">{permission}</span>
                                  </div>
                                  <Switch
                                    checked={value}
                                    disabled={selectedRole.isSystem}
                                    onCheckedChange={(checked) => {
                                      // In a real app, you would update the role permissions here
                                      console.log(`Changed ${category}.${permission} to ${checked}`)
                                    }}
                                  />
                                </div>
                              ))}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </TabsContent>
                  <TabsContent value="users">
                    <div className="text-center py-8 text-gray-500">User list would be displayed here.</div>
                  </TabsContent>
                </Tabs>
              </CardContent>
            </Card>
          ) : (
            <div className="h-full flex items-center justify-center bg-cream-50 rounded-lg border border-dashed border-gray-200 p-8">
              <div className="text-center">
                <Shield className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-700">Select a role</h3>
                <p className="text-gray-500 mt-2">Choose a role from the list to view and manage its permissions.</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}


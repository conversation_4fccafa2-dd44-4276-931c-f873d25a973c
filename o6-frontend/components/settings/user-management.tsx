"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Badge } from "@/components/ui/badge"
import { MoreHorizontal, Search, Mail, Shield, UserPlus } from "lucide-react"

// Define user types
interface User {
  id: string
  name: string
  email: string
  role: string
  status: "active" | "invited" | "inactive"
  lastActive?: Date
}

export function UserManagement() {
  // Mock users data
  const [users, setUsers] = useState<User[]>([
    {
      id: "1",
      name: "<PERSON>",
      email: "<EMAIL>",
      role: "Owner",
      status: "active",
      lastActive: new Date(),
    },
    {
      id: "2",
      name: "<PERSON>",
      email: "<EMAIL>",
      role: "Admin",
      status: "active",
      lastActive: new Date(Date.now() - 86400000), // 1 day ago
    },
    {
      id: "3",
      name: "Michael Chen",
      email: "<EMAIL>",
      role: "HR Manager",
      status: "active",
      lastActive: new Date(Date.now() - 172800000), // 2 days ago
    },
    {
      id: "4",
      name: "Emily Davis",
      email: "<EMAIL>",
      role: "Recruiter",
      status: "invited",
    },
  ])

  const [searchQuery, setSearchQuery] = useState("")
  const [isAddUserOpen, setIsAddUserOpen] = useState(false)
  const [newUser, setNewUser] = useState({
    name: "",
    email: "",
    role: "User",
  })

  // Filter users based on search query
  const filteredUsers = searchQuery
    ? users.filter(
        (user) =>
          user.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          user.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
          user.role.toLowerCase().includes(searchQuery.toLowerCase()),
      )
    : users

  const handleAddUser = () => {
    const newUserData: User = {
      id: `${users.length + 1}`,
      name: newUser.name,
      email: newUser.email,
      role: newUser.role,
      status: "invited",
    }

    setUsers([...users, newUserData])
    setNewUser({ name: "", email: "", role: "User" })
    setIsAddUserOpen(false)
  }

  return (
    <div className="space-y-6">
      <Card className="border-gray-200/50 shadow-sm">
        <CardHeader className="flex flex-row items-center justify-between">
          <div>
            <CardTitle>Users</CardTitle>
            <CardDescription>Manage users and their access to your organization.</CardDescription>
          </div>
          <Dialog open={isAddUserOpen} onOpenChange={setIsAddUserOpen}>
            <DialogTrigger asChild>
              <Button className="bg-forest-600 hover:bg-forest-700 text-white">
                <UserPlus className="h-4 w-4 mr-2" />
                Add User
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Add New User</DialogTitle>
                <DialogDescription>
                  Invite a new user to join your organization. They will receive an email invitation.
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4 py-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Name</Label>
                  <Input
                    id="name"
                    value={newUser.name}
                    onChange={(e) => setNewUser({ ...newUser, name: e.target.value })}
                    placeholder="Enter user's name"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    type="email"
                    value={newUser.email}
                    onChange={(e) => setNewUser({ ...newUser, email: e.target.value })}
                    placeholder="Enter user's email"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="role">Role</Label>
                  <select
                    id="role"
                    value={newUser.role}
                    onChange={(e) => setNewUser({ ...newUser, role: e.target.value })}
                    className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                  >
                    <option value="Admin">Admin</option>
                    <option value="HR Manager">HR Manager</option>
                    <option value="Recruiter">Recruiter</option>
                    <option value="User">User</option>
                  </select>
                </div>
              </div>
              <DialogFooter>
                <Button
                  variant="outline"
                  onClick={() => setIsAddUserOpen(false)}
                  className="border-gray-200 text-gray-700"
                >
                  Cancel
                </Button>
                <Button onClick={handleAddUser} className="bg-forest-600 hover:bg-forest-700 text-white">
                  Send Invitation
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center">
              <div className="relative flex-1 max-w-sm">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search users..."
                  className="pl-8"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
            </div>

            <div className="border rounded-md">
              <div className="grid grid-cols-12 gap-2 p-4 border-b bg-muted/50 text-sm font-medium text-gray-500">
                <div className="col-span-3">Name</div>
                <div className="col-span-4">Email</div>
                <div className="col-span-2">Role</div>
                <div className="col-span-2">Status</div>
                <div className="col-span-1"></div>
              </div>
              <div className="divide-y">
                {filteredUsers.map((user) => (
                  <div key={user.id} className="grid grid-cols-12 gap-2 p-4 items-center">
                    <div className="col-span-3 font-medium">{user.name}</div>
                    <div className="col-span-4 text-gray-600">{user.email}</div>
                    <div className="col-span-2">
                      <div className="flex items-center">
                        {user.role === "Owner" && <Shield className="h-4 w-4 text-forest-600 mr-1" />}
                        <span>{user.role}</span>
                      </div>
                    </div>
                    <div className="col-span-2">
                      <Badge
                        variant="outline"
                        className={
                          user.status === "active"
                            ? "bg-green-100 text-green-800 border-green-200"
                            : user.status === "invited"
                              ? "bg-blue-100 text-blue-800 border-blue-200"
                              : "bg-gray-100 text-gray-800 border-gray-200"
                        }
                      >
                        {user.status === "active" ? "Active" : user.status === "invited" ? "Invited" : "Inactive"}
                      </Badge>
                    </div>
                    <div className="col-span-1 text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon" className="h-8 w-8">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem>Edit User</DropdownMenuItem>
                          <DropdownMenuItem>Change Role</DropdownMenuItem>
                          {user.status === "invited" && (
                            <DropdownMenuItem>
                              <Mail className="h-4 w-4 mr-2" />
                              Resend Invitation
                            </DropdownMenuItem>
                          )}
                          {user.role !== "Owner" && (
                            <DropdownMenuItem className="text-red-600">
                              {user.status === "active" ? "Deactivate User" : "Delete User"}
                            </DropdownMenuItem>
                          )}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {filteredUsers.length === 0 && (
              <div className="text-center py-8 text-gray-500">No users found matching your search criteria.</div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}


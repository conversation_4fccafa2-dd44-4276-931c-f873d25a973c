"use client"

import type React from "react"

import { useState } from "react"
import { Send, Sparkles, X } from "lucide-react"
import { <PERSON>ton } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { ScrollArea } from "@/components/ui/scroll-area"
import type { Note } from "./notes-module"

interface AiAssistantProps {
  activeNote: Note | null
  onClose: () => void
  onInsertContent: (content: string) => void
}

type Message = {
  id: string
  role: "user" | "assistant"
  content: string
}

export function AiAssistant({ activeNote, onClose, onInsertContent }: AiAssistantProps) {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: "1",
      role: "assistant",
      content: "Hello! I'm your AI assistant. How can I help with your notes today?",
    },
  ])
  const [input, setInput] = useState("")
  const [isLoading, setIsLoading] = useState(false)

  const handleSendMessage = () => {
    if (!input.trim()) return

    // Add user message
    const userMessage: Message = {
      id: `msg-${messages.length + 1}`,
      role: "user",
      content: input,
    }

    setMessages([...messages, userMessage])
    setInput("")
    setIsLoading(true)

    // Simulate AI response
    setTimeout(() => {
      const responses = [
        "I've analyzed your note and here are some suggestions for improvement.",
        "Based on your content, you might want to consider adding more details about the technical requirements.",
        "Here's a draft section you could add to your note:\n\n**Technical Requirements**\n- Experience with Python 3.8+\n- Knowledge of Django or Flask\n- Familiarity with RESTful APIs\n- Understanding of database design principles",
        "I've summarized the key points from your note. Would you like me to expand on any specific section?",
        "Your note looks great! I've noticed it's similar to other job descriptions in your collection. Would you like me to highlight the unique aspects?",
      ]

      const aiMessage: Message = {
        id: `msg-${messages.length + 2}`,
        role: "assistant",
        content: responses[Math.floor(Math.random() * responses.length)],
      }

      setMessages((prev) => [...prev, aiMessage])
      setIsLoading(false)
    }, 1500)
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  const handleInsertContent = (content: string) => {
    onInsertContent(content)
  }

  const suggestedPrompts = [
    "Summarize this note",
    "Improve the language",
    "Generate a conclusion",
    "Add technical requirements",
    "Create an introduction",
  ]

  return (
    <div className="h-full flex flex-col">
      <div className="p-3 border-b border-border flex justify-between items-center">
        <div className="flex items-center">
          <Sparkles className="h-4 w-4 mr-2 text-primary" />
          <h3 className="font-medium">AI Assistant</h3>
        </div>
        <Button variant="ghost" size="icon" onClick={onClose}>
          <X className="h-4 w-4" />
        </Button>
      </div>

      <ScrollArea className="flex-1 p-4">
        <div className="space-y-4">
          {messages.map((message) => (
            <div key={message.id} className={`flex ${message.role === "user" ? "justify-end" : "justify-start"}`}>
              <div
                className={`max-w-[80%] rounded-lg p-3 ${
                  message.role === "user" ? "bg-primary text-primary-foreground" : "bg-muted"
                }`}
              >
                {message.role === "assistant" && (
                  <div className="flex items-center mb-1">
                    <Sparkles className="h-3 w-3 mr-1 text-primary" />
                    <span className="text-xs font-medium">AI Assistant</span>
                  </div>
                )}
                <div className="whitespace-pre-wrap">{message.content}</div>
                {message.role === "assistant" && message.content.includes("**Technical Requirements**") && (
                  <Button
                    variant="link"
                    size="sm"
                    className="mt-2 p-0 h-auto text-xs"
                    onClick={() => handleInsertContent(message.content.split("\n\n")[1])}
                  >
                    Insert into note
                  </Button>
                )}
              </div>
            </div>
          ))}

          {isLoading && (
            <div className="flex justify-start">
              <div className="max-w-[80%] rounded-lg p-3 bg-muted">
                <div className="flex items-center">
                  <Sparkles className="h-3 w-3 mr-1 text-primary" />
                  <span className="text-xs font-medium">AI Assistant</span>
                </div>
                <div className="mt-1 flex space-x-1">
                  <div className="h-2 w-2 rounded-full bg-muted-foreground animate-bounce" />
                  <div
                    className="h-2 w-2 rounded-full bg-muted-foreground animate-bounce"
                    style={{ animationDelay: "0.2s" }}
                  />
                  <div
                    className="h-2 w-2 rounded-full bg-muted-foreground animate-bounce"
                    style={{ animationDelay: "0.4s" }}
                  />
                </div>
              </div>
            </div>
          )}
        </div>
      </ScrollArea>

      <div className="p-3 border-t border-border">
        <div className="mb-3">
          <p className="text-xs text-muted-foreground mb-2">Suggested prompts:</p>
          <div className="flex flex-wrap gap-2">
            {suggestedPrompts.map((prompt) => (
              <Button key={prompt} variant="outline" size="sm" className="text-xs h-7" onClick={() => setInput(prompt)}>
                {prompt}
              </Button>
            ))}
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <Input
            value={input}
            onChange={(e) => setInput(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder="Ask the AI assistant..."
            className="flex-1"
          />
          <Button size="icon" onClick={handleSendMessage} disabled={isLoading}>
            <Send className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  )
}


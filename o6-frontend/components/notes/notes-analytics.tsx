"use client"

import { Calendar, FileText, Tag, X } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import type { Note } from "./notes-module"

interface NotesAnalyticsProps {
  notes: Note[]
  onClose: () => void
}

export function NotesAnalytics({ notes, onClose }: NotesAnalyticsProps) {
  // Calculate analytics data
  const totalNotes = notes.length
  const totalTags = [...new Set(notes.flatMap((note) => note.tags))].length
  const totalFolders = [...new Set(notes.map((note) => note.folder))].length

  // Notes created per month (last 6 months)
  const getMonthlyData = () => {
    const data: Record<string, number> = {}
    const today = new Date()

    // Initialize last 6 months with 0 counts
    for (let i = 5; i >= 0; i--) {
      const d = new Date(today.getFullYear(), today.getMonth() - i, 1)
      const monthYear = d.toLocaleDateString("en-US", { month: "short", year: "2-digit" })
      data[monthYear] = 0
    }

    // Count notes per month
    notes.forEach((note) => {
      const date = new Date(note.createdAt)
      const monthYear = date.toLocaleDateString("en-US", { month: "short", year: "2-digit" })

      // Only count if it's within the last 6 months
      const sixMonthsAgo = new Date()
      sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6)

      if (date >= sixMonthsAgo && data[monthYear] !== undefined) {
        data[monthYear]++
      }
    })

    return data
  }

  const monthlyData = getMonthlyData()
  const maxMonthlyCount = Math.max(...Object.values(monthlyData), 5) // Minimum of 5 for scale

  // Get top tags
  const getTopTags = () => {
    const tagCounts: Record<string, number> = {}

    notes.forEach((note) => {
      note.tags.forEach((tag) => {
        tagCounts[tag] = (tagCounts[tag] || 0) + 1
      })
    })

    return Object.entries(tagCounts)
      .sort((a, b) => b[1] - a[1])
      .slice(0, 5)
  }

  const topTags = getTopTags()
  const maxTagCount = Math.max(...topTags.map(([_, count]) => count), 5) // Minimum of 5 for scale

  return (
    <Card className="w-[380px] shadow-lg">
      <CardHeader className="pb-3">
        <div className="flex justify-between items-center">
          <CardTitle>Notes Analytics</CardTitle>
          <Button variant="ghost" size="icon" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="grid grid-cols-3 gap-4 mb-6">
          <div className="flex flex-col items-center justify-center p-3 bg-muted/50 rounded-lg">
            <FileText className="h-5 w-5 text-primary mb-1" />
            <span className="text-2xl font-bold">{totalNotes}</span>
            <span className="text-xs text-muted-foreground">Notes</span>
          </div>
          <div className="flex flex-col items-center justify-center p-3 bg-muted/50 rounded-lg">
            <Tag className="h-5 w-5 text-primary mb-1" />
            <span className="text-2xl font-bold">{totalTags}</span>
            <span className="text-xs text-muted-foreground">Tags</span>
          </div>
          <div className="flex flex-col items-center justify-center p-3 bg-muted/50 rounded-lg">
            <Calendar className="h-5 w-5 text-primary mb-1" />
            <span className="text-2xl font-bold">{totalFolders}</span>
            <span className="text-xs text-muted-foreground">Folders</span>
          </div>
        </div>

        <Tabs defaultValue="activity">
          <TabsList className="w-full mb-4">
            <TabsTrigger value="activity" className="flex-1">
              Activity
            </TabsTrigger>
            <TabsTrigger value="tags" className="flex-1">
              Top Tags
            </TabsTrigger>
          </TabsList>

          <TabsContent value="activity" className="mt-0">
            <h4 className="text-sm font-medium mb-2">Notes Created (Last 6 Months)</h4>
            <div className="space-y-2">
              {Object.entries(monthlyData).map(([month, count]) => (
                <div key={month} className="flex items-center">
                  <div className="w-16 text-xs">{month}</div>
                  <div className="flex-1 h-7 bg-muted rounded-sm overflow-hidden">
                    <div
                      className="h-full bg-primary rounded-sm"
                      style={{ width: `${(count / maxMonthlyCount) * 100}%` }}
                    />
                  </div>
                  <div className="w-8 text-xs text-right">{count}</div>
                </div>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="tags" className="mt-0">
            <h4 className="text-sm font-medium mb-2">Most Used Tags</h4>
            {topTags.length > 0 ? (
              <div className="space-y-2">
                {topTags.map(([tag, count]) => (
                  <div key={tag} className="flex items-center">
                    <div className="w-24 text-xs truncate">{tag}</div>
                    <div className="flex-1 h-7 bg-muted rounded-sm overflow-hidden">
                      <div
                        className="h-full bg-primary rounded-sm"
                        style={{ width: `${(count / maxTagCount) * 100}%` }}
                      />
                    </div>
                    <div className="w-8 text-xs text-right">{count}</div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center h-[150px] text-center">
                <Tag className="h-8 w-8 text-muted-foreground mb-2" />
                <p className="text-muted-foreground">No tags found</p>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}


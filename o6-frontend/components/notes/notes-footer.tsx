"use client"

import { useState } from "react"
import { BarChart<PERSON>, Bell, CalendarCheck, Sparkles } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { cn } from "@/lib/utils"
import type { Note } from "./notes-module"

interface NotesFooterProps {
  activeNote: Note | null
  notes: Note[]
  isAiAssistantOpen: boolean
  toggleAiAssistant: () => void
  onSelectNote: (note: Note) => void
}

export function NotesFooter({
  activeNote,
  notes,
  isAiAssistantOpen,
  toggleAiAssistant,
  onSelectNote,
}: NotesFooterProps) {
  const [activePanel, setActivePanel] = useState<string | null>(null)

  const togglePanel = (panel: string) => {
    console.log("Toggle panel:", panel)
    setActivePanel(activePanel === panel ? null : panel)
  }

  return (
    <div className="p-2 flex justify-between items-center">
      <div className="flex items-center space-x-2">
        <Button
          variant="ghost"
          size="sm"
          onClick={toggleAiAssistant}
          className={cn("flex items-center", isAiAssistantOpen && "bg-primary/10")}
        >
          <Sparkles className="h-4 w-4 mr-2 text-primary" />
          AI Assistant
        </Button>
      </div>
      <div className="flex items-center space-x-2">
        {/* Always show these buttons for debugging */}
        <Button
          variant="outline"
          size="sm"
          title="Notifications"
          onClick={() => togglePanel("notifications")}
          className={activePanel === "notifications" ? "bg-primary/10" : ""}
        >
          <Bell className="h-4 w-4 mr-2" />
          Notifications
        </Button>
        <Button
          variant="outline"
          size="sm"
          title="Calendar"
          onClick={() => togglePanel("calendar")}
          className={activePanel === "calendar" ? "bg-primary/10" : ""}
        >
          <CalendarCheck className="h-4 w-4 mr-2" />
          Calendar
        </Button>
        <Button
          variant="outline"
          size="sm"
          title="Analytics"
          onClick={() => togglePanel("analytics")}
          className={activePanel === "analytics" ? "bg-primary/10" : ""}
        >
          <BarChart4 className="h-4 w-4 mr-2" />
          Analytics
        </Button>
      </div>

      {/* Simple panel display with more prominent styling */}
      {activePanel && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg shadow-xl max-w-md w-full">
            <h3 className="text-xl font-bold mb-4">
              {activePanel === "notifications" && "Notifications Panel"}
              {activePanel === "calendar" && "Calendar Panel"}
              {activePanel === "analytics" && "Analytics Panel"}
            </h3>
            <p className="mb-4">This is the {activePanel} panel content.</p>
            <Button onClick={() => setActivePanel(null)}>Close Panel</Button>
          </div>
        </div>
      )}
    </div>
  )
}


"use client"

import { useState } from "react"
import { ChevronLeft, ChevronRight, FileText, X } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { ScrollArea } from "@/components/ui/scroll-area"
import { cn } from "@/lib/utils"
import type { Note } from "./notes-module"

interface NotesCalendarProps {
  notes: Note[]
  onSelectNote: (note: Note) => void
  onClose: () => void
}

export function NotesCalendar({ notes, onSelectNote, onClose }: NotesCalendarProps) {
  const [currentDate, setCurrentDate] = useState(new Date())
  const [selectedDate, setSelectedDate] = useState<Date | null>(null)

  const getDaysInMonth = (year: number, month: number) => {
    return new Date(year, month + 1, 0).getDate()
  }

  const getFirstDayOfMonth = (year: number, month: number) => {
    return new Date(year, month, 1).getDay()
  }

  const prevMonth = () => {
    setCurrentDate(new Date(currentDate.getFullYear(), currentDate.getMonth() - 1, 1))
    setSelectedDate(null)
  }

  const nextMonth = () => {
    setCurrentDate(new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 1))
    setSelectedDate(null)
  }

  const handleDateClick = (day: number) => {
    const newDate = new Date(currentDate.getFullYear(), currentDate.getMonth(), day)
    setSelectedDate(newDate)
  }

  const year = currentDate.getFullYear()
  const month = currentDate.getMonth()
  const daysInMonth = getDaysInMonth(year, month)
  const firstDayOfMonth = getFirstDayOfMonth(year, month)
  const monthName = currentDate.toLocaleString("default", { month: "long" })

  // Get notes for the selected date
  const getNotesForDate = (date: Date) => {
    if (!date) return []

    return notes.filter((note) => {
      const noteDate = new Date(note.updatedAt)
      return (
        noteDate.getDate() === date.getDate() &&
        noteDate.getMonth() === date.getMonth() &&
        noteDate.getFullYear() === date.getFullYear()
      )
    })
  }

  // Check if a date has notes
  const dateHasNotes = (day: number) => {
    const date = new Date(year, month, day)
    return notes.some((note) => {
      const noteDate = new Date(note.updatedAt)
      return (
        noteDate.getDate() === date.getDate() &&
        noteDate.getMonth() === date.getMonth() &&
        noteDate.getFullYear() === date.getFullYear()
      )
    })
  }

  const selectedDateNotes = selectedDate ? getNotesForDate(selectedDate) : []

  return (
    <Card className="w-[380px] shadow-lg">
      <CardHeader className="pb-3">
        <div className="flex justify-between items-center">
          <CardTitle>Calendar</CardTitle>
          <Button variant="ghost" size="icon" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="flex justify-between items-center mb-4">
          <Button variant="ghost" size="icon" onClick={prevMonth}>
            <ChevronLeft className="h-4 w-4" />
          </Button>
          <h3 className="font-medium">
            {monthName} {year}
          </h3>
          <Button variant="ghost" size="icon" onClick={nextMonth}>
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>

        <div className="grid grid-cols-7 gap-1 text-center mb-2">
          {["Su", "Mo", "Tu", "We", "Th", "Fr", "Sa"].map((day) => (
            <div key={day} className="text-xs font-medium text-muted-foreground">
              {day}
            </div>
          ))}
        </div>

        <div className="grid grid-cols-7 gap-1">
          {Array.from({ length: firstDayOfMonth }).map((_, index) => (
            <div key={`empty-${index}`} className="h-8" />
          ))}

          {Array.from({ length: daysInMonth }).map((_, index) => {
            const day = index + 1
            const isToday =
              new Date().getDate() === day && new Date().getMonth() === month && new Date().getFullYear() === year

            const isSelected =
              selectedDate?.getDate() === day &&
              selectedDate?.getMonth() === month &&
              selectedDate?.getFullYear() === year

            const hasNotes = dateHasNotes(day)

            return (
              <Button
                key={day}
                variant="ghost"
                className={cn(
                  "h-8 w-full p-0 relative",
                  isToday && "font-bold text-primary",
                  isSelected && "bg-primary/10",
                )}
                onClick={() => handleDateClick(day)}
              >
                {day}
                {hasNotes && (
                  <div className="absolute bottom-1 left-1/2 transform -translate-x-1/2 w-1 h-1 rounded-full bg-primary" />
                )}
              </Button>
            )
          })}
        </div>

        {selectedDate && (
          <div className="mt-4">
            <h4 className="font-medium text-sm mb-2">Notes for {selectedDate.toLocaleDateString()}</h4>
            <ScrollArea className="h-[180px]">
              {selectedDateNotes.length > 0 ? (
                <div className="space-y-2">
                  {selectedDateNotes.map((note) => (
                    <Button
                      key={note.id}
                      variant="outline"
                      className="w-full justify-start text-left h-auto py-2"
                      onClick={() => onSelectNote(note)}
                    >
                      <FileText className="h-4 w-4 mr-2 flex-shrink-0" />
                      <div className="truncate">{note.title}</div>
                    </Button>
                  ))}
                </div>
              ) : (
                <p className="text-sm text-muted-foreground text-center py-4">No notes for this date</p>
              )}
            </ScrollArea>
          </div>
        )}
      </CardContent>
    </Card>
  )
}


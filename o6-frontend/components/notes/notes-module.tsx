"use client"

import type React from "react"

import { useState, useEffect } from "react"
import {
  ChevronDown,
  Copy,
  Download,
  Edit,
  FileText,
  History,
  MoreHorizontal,
  Plus,
  Save,
  Share2,
  Sparkles,
  Undo,
  X,
  Search,
  Tag,
  Folder,
  Menu,
  Send,
} from "lucide-react"

import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Separator } from "@/components/ui/separator"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { ScrollArea } from "@/components/ui/scroll-area"
import { cn } from "@/lib/utils"

// First, import the NotesFooter component at the top of the file
import { NotesFooter } from "./notes-footer"

export type Note = {
  id: string
  title: string
  content: string
  tags: string[]
  createdAt: Date
  updatedAt: Date
  folder: string
  versions: {
    id: string
    content: string
    createdAt: Date
  }[]
}

export type NoteBlockType = {
  id: string
  type: "text" | "heading" | "subheading" | "bullet" | "numbered" | "code" | "image" | "divider"
  content: string
}

export function NotesModule() {
  const [sidebarOpen, setSidebarOpen] = useState(true)
  const [activeNote, setActiveNote] = useState<Note | null>(null)
  const [notes, setNotes] = useState<Note[]>([])
  const [folders, setFolders] = useState<string[]>(["Projects", "Clients", "Meetings", "Resources"])
  const [tags, setTags] = useState<string[]>(["Important", "Follow-up", "Draft", "Final"])
  const [isAiAssistantOpen, setIsAiAssistantOpen] = useState(false)
  const [searchQuery, setSearchQuery] = useState("")
  const [editMode, setEditMode] = useState(false)
  const [noteBlocks, setNoteBlocks] = useState<NoteBlockType[]>([])
  const [activeTab, setActiveTab] = useState("all")

  // Mock data initialization
  useEffect(() => {
    const mockNotes: Note[] = [
      {
        id: "1",
        title: "Python Developer - Job Description",
        content: "We are seeking a highly skilled and motivated Python Developer...",
        tags: ["Job Description", "Technical", "Recruitment"],
        createdAt: new Date("2023-03-15"),
        updatedAt: new Date("2023-03-18"),
        folder: "Recruitment",
        versions: [
          {
            id: "v1",
            content: "Initial draft of Python Developer job description",
            createdAt: new Date("2023-03-15"),
          },
          {
            id: "v2",
            content: "Updated Python Developer job description with additional requirements",
            createdAt: new Date("2023-03-18"),
          },
        ],
      },
      {
        id: "2",
        title: "Q1 Sales Strategy",
        content: "Our Q1 sales strategy focuses on expanding into new markets...",
        tags: ["Sales", "Strategy", "Q1"],
        createdAt: new Date("2023-02-10"),
        updatedAt: new Date("2023-02-12"),
        folder: "Projects",
        versions: [
          {
            id: "v1",
            content: "Initial Q1 sales strategy outline",
            createdAt: new Date("2023-02-10"),
          },
        ],
      },
      {
        id: "3",
        title: "Client Meeting - Acme Corp",
        content: "Meeting notes from discussion with Acme Corp about their new project requirements...",
        tags: ["Meeting Notes", "Client", "Project"],
        createdAt: new Date("2023-03-05"),
        updatedAt: new Date("2023-03-05"),
        folder: "Clients",
        versions: [
          {
            id: "v1",
            content: "Notes from Acme Corp meeting",
            createdAt: new Date("2023-03-05"),
          },
        ],
      },
    ]

    const initialBlocks: NoteBlockType[] = [
      {
        id: "block-1",
        type: "heading",
        content: "Python Developer - Job Description",
      },
      {
        id: "block-2",
        type: "subheading",
        content: "Overview",
      },
      {
        id: "block-3",
        type: "text",
        content:
          "We are seeking a highly skilled and motivated Python Developer to join our dynamic team. The ideal candidate will have a strong background in software development, a passion for coding, and the ability to solve complex problems.",
      },
      {
        id: "block-4",
        type: "subheading",
        content: "Key Responsibilities",
      },
      {
        id: "block-5",
        type: "bullet",
        content: "Developing and maintaining Python-based applications to meet business requirements.",
      },
      {
        id: "block-6",
        type: "bullet",
        content: "Writing clean, efficient, and reusable code following best practices.",
      },
      {
        id: "block-7",
        type: "bullet",
        content:
          "Collaborating with cross-functional teams, including designers, product managers, and other developers.",
      },
    ]

    setNotes(mockNotes)
    setActiveNote(mockNotes[0])
    setNoteBlocks(initialBlocks)
  }, [])

  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen)
  }

  const toggleAiAssistant = () => {
    setIsAiAssistantOpen(!isAiAssistantOpen)
  }

  const handleNoteSelect = (note: Note) => {
    setActiveNote(note)
    setEditMode(false)
  }

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value)
  }

  const filteredNotes = notes.filter(
    (note) =>
      note.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      note.content.toLowerCase().includes(searchQuery.toLowerCase()) ||
      note.tags.some((tag) => tag.toLowerCase().includes(searchQuery.toLowerCase())),
  )

  const handleAddBlock = (type: NoteBlockType["type"]) => {
    const newBlock: NoteBlockType = {
      id: `block-${noteBlocks.length + 1}`,
      type,
      content: "",
    }
    setNoteBlocks([...noteBlocks, newBlock])
  }

  const handleUpdateBlock = (id: string, content: string) => {
    setNoteBlocks(noteBlocks.map((block) => (block.id === id ? { ...block, content } : block)))
  }

  const handleDeleteBlock = (id: string) => {
    setNoteBlocks(noteBlocks.filter((block) => block.id !== id))
  }

  const handleSaveNote = () => {
    if (!activeNote) return

    // In a real application, you would save to your backend here
    const updatedNote = {
      ...activeNote,
      content: noteBlocks.map((block) => block.content).join("\n"),
      updatedAt: new Date(),
      versions: [
        ...activeNote.versions,
        {
          id: `v${activeNote.versions.length + 1}`,
          content: noteBlocks.map((block) => block.content).join("\n"),
          createdAt: new Date(),
        },
      ],
    }

    setNotes(notes.map((note) => (note.id === activeNote.id ? updatedNote : note)))
    setActiveNote(updatedNote)
    setEditMode(false)
  }

  const handleCreateNewNote = () => {
    const newNote: Note = {
      id: `note-${notes.length + 1}`,
      title: "Untitled Note",
      content: "",
      tags: [],
      createdAt: new Date(),
      updatedAt: new Date(),
      folder: "Drafts",
      versions: [],
    }

    setNotes([...notes, newNote])
    setActiveNote(newNote)
    setNoteBlocks([
      {
        id: "block-1",
        type: "heading",
        content: "Untitled Note",
      },
    ])
    setEditMode(true)
  }

  return (
    <div className="flex h-screen bg-background">
      {/* Sidebar */}
      <div
        className={cn(
          "border-r border-border transition-all duration-300 bg-card",
          sidebarOpen ? "w-64" : "w-0 overflow-hidden",
        )}
      >
        <div className="h-full flex flex-col">
          <div className="p-4 flex justify-between items-center">
            <h2 className="font-semibold text-lg">Notes</h2>
            <Button size="sm" onClick={handleCreateNewNote}>
              <Plus className="h-4 w-4 mr-1" />
              New
            </Button>
          </div>

          <div className="px-4 pb-2">
            <div className="relative">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input placeholder="Search notes..." className="pl-8" value={searchQuery} onChange={handleSearch} />
            </div>
          </div>

          <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col">
            <TabsList className="mx-4 mb-2">
              <TabsTrigger value="all" className="flex-1">
                All
              </TabsTrigger>
              <TabsTrigger value="folders" className="flex-1">
                Folders
              </TabsTrigger>
              <TabsTrigger value="tags" className="flex-1">
                Tags
              </TabsTrigger>
            </TabsList>

            <ScrollArea className="flex-1">
              <TabsContent value="all" className="m-0 p-0">
                <div className="px-2">
                  {notes.map((note) => (
                    <Button
                      key={note.id}
                      variant="ghost"
                      className={cn("w-full justify-start mb-1 font-normal", activeNote?.id === note.id && "bg-accent")}
                      onClick={() => handleNoteSelect(note)}
                    >
                      <FileText className="h-4 w-4 mr-2" />
                      <span className="truncate">{note.title}</span>
                    </Button>
                  ))}
                </div>
              </TabsContent>

              <TabsContent value="folders" className="m-0 p-0">
                <div className="px-2">
                  {folders.map((folder) => (
                    <div key={folder}>
                      <Button variant="ghost" className="w-full justify-start mb-1 font-medium">
                        <ChevronDown className="h-4 w-4 mr-2" />
                        <Folder className="h-4 w-4 mr-2" />
                        <span>{folder}</span>
                      </Button>

                      {notes
                        .filter((note) => note.folder === folder)
                        .map((note) => (
                          <Button
                            key={note.id}
                            variant="ghost"
                            className={cn(
                              "w-full justify-start mb-1 font-normal pl-9",
                              activeNote?.id === note.id && "bg-accent",
                            )}
                            onClick={() => handleNoteSelect(note)}
                          >
                            <FileText className="h-4 w-4 mr-2" />
                            <span className="truncate">{note.title}</span>
                          </Button>
                        ))}
                    </div>
                  ))}
                </div>
              </TabsContent>

              <TabsContent value="tags" className="m-0 p-0">
                <div className="px-2">
                  {tags.map((tag) => (
                    <div key={tag}>
                      <Button variant="ghost" className="w-full justify-start mb-1 font-medium">
                        <ChevronDown className="h-4 w-4 mr-2" />
                        <Tag className="h-4 w-4 mr-2" />
                        <span>{tag}</span>
                      </Button>
                    </div>
                  ))}
                </div>
              </TabsContent>
            </ScrollArea>
          </Tabs>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Header */}
        <header className="border-b border-border p-2 flex justify-between items-center bg-card">
          <div className="flex items-center">
            <Button variant="ghost" size="icon" onClick={toggleSidebar}>
              <Menu className="h-5 w-5" />
            </Button>
            {activeNote && (
              <div className="ml-2 flex items-center">
                <h1 className="font-medium truncate max-w-md">{activeNote.title}</h1>
                <span className="text-xs text-muted-foreground ml-2">
                  Last updated: {activeNote.updatedAt.toLocaleDateString()}
                </span>
              </div>
            )}
          </div>

          {activeNote && (
            <div className="flex items-center space-x-1">
              {editMode ? (
                <>
                  <Button variant="outline" size="sm" onClick={() => setEditMode(false)}>
                    Cancel
                  </Button>
                  <Button size="sm" onClick={handleSaveNote}>
                    <Save className="h-4 w-4 mr-1" />
                    Save
                  </Button>
                </>
              ) : (
                <>
                  <Button variant="outline" size="sm" onClick={() => setEditMode(true)}>
                    <Edit className="h-4 w-4 mr-1" />
                    Edit
                  </Button>

                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon">
                        <MoreHorizontal className="h-5 w-5" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem>
                        <Copy className="h-4 w-4 mr-2" />
                        Copy
                      </DropdownMenuItem>
                      <DropdownMenuItem>
                        <Download className="h-4 w-4 mr-2" />
                        Download
                      </DropdownMenuItem>
                      <DropdownMenuItem>
                        <Share2 className="h-4 w-4 mr-2" />
                        Share
                      </DropdownMenuItem>
                      <DropdownMenuItem>
                        <History className="h-4 w-4 mr-2" />
                        Version History
                      </DropdownMenuItem>
                      <DropdownMenuItem>
                        <Undo className="h-4 w-4 mr-2" />
                        Revert Changes
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </>
              )}
            </div>
          )}
        </header>

        {/* Note Content */}
        <div className="flex-1 flex overflow-hidden">
          <div className="flex-1 overflow-auto p-6">
            {activeNote ? (
              <div className="max-w-4xl mx-auto">
                {editMode ? (
                  <div className="space-y-4">
                    {noteBlocks.map((block) => (
                      <div
                        key={block.id}
                        className="group relative border border-transparent hover:border-border rounded-md p-2"
                      >
                        {block.type === "heading" && (
                          <Input
                            value={block.content}
                            onChange={(e) => handleUpdateBlock(block.id, e.target.value)}
                            className="text-2xl font-bold border-none focus-visible:ring-0 p-0"
                            placeholder="Heading"
                          />
                        )}

                        {block.type === "subheading" && (
                          <Input
                            value={block.content}
                            onChange={(e) => handleUpdateBlock(block.id, e.target.value)}
                            className="text-xl font-semibold border-none focus-visible:ring-0 p-0"
                            placeholder="Subheading"
                          />
                        )}

                        {block.type === "text" && (
                          <textarea
                            value={block.content}
                            onChange={(e) => handleUpdateBlock(block.id, e.target.value)}
                            className="w-full min-h-[100px] border-none focus:outline-none p-0 resize-none bg-transparent"
                            placeholder="Start typing..."
                          />
                        )}

                        {block.type === "bullet" && (
                          <div className="flex items-start">
                            <span className="mr-2 mt-1.5">•</span>
                            <textarea
                              value={block.content}
                              onChange={(e) => handleUpdateBlock(block.id, e.target.value)}
                              className="w-full min-h-[60px] border-none focus:outline-none p-0 resize-none bg-transparent"
                              placeholder="Bullet point"
                            />
                          </div>
                        )}

                        {block.type === "numbered" && (
                          <div className="flex items-start">
                            <span className="mr-2 font-medium">1.</span>
                            <textarea
                              value={block.content}
                              onChange={(e) => handleUpdateBlock(block.id, e.target.value)}
                              className="w-full min-h-[60px] border-none focus:outline-none p-0 resize-none bg-transparent"
                              placeholder="Numbered item"
                            />
                          </div>
                        )}

                        {block.type === "code" && (
                          <textarea
                            value={block.content}
                            onChange={(e) => handleUpdateBlock(block.id, e.target.value)}
                            className="w-full min-h-[120px] font-mono text-sm border-none focus:outline-none p-2 bg-muted rounded-md"
                            placeholder="// Code block"
                          />
                        )}

                        <Button
                          variant="ghost"
                          size="icon"
                          className="absolute -right-2 -top-2 opacity-0 group-hover:opacity-100 focus:opacity-100"
                          onClick={() => handleDeleteBlock(block.id)}
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      </div>
                    ))}
                    <div className="flex items-center space-x-2 mt-4 p-2 border border-dashed border-border rounded-md">
                      <Button variant="ghost" size="sm" onClick={() => handleAddBlock("text")}>
                        Text
                      </Button>
                      <Button variant="ghost" size="sm" onClick={() => handleAddBlock("heading")}>
                        Heading
                      </Button>
                      <Button variant="ghost" size="sm" onClick={() => handleAddBlock("bullet")}>
                        Bullet
                      </Button>
                      <Button variant="ghost" size="sm" onClick={() => handleAddBlock("numbered")}>
                        Numbered
                      </Button>
                      <Button variant="ghost" size="sm" onClick={() => handleAddBlock("code")}>
                        Code
                      </Button>
                      <Button variant="ghost" size="sm" onClick={() => handleAddBlock("divider")}>
                        Divider
                      </Button>
                    </div>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {noteBlocks.map((block) => (
                      <div key={block.id} className="py-1">
                        {block.type === "heading" && <h1 className="text-3xl font-bold">{block.content}</h1>}
                        {block.type === "subheading" && <h2 className="text-xl font-semibold mt-6">{block.content}</h2>}
                        {block.type === "text" && <p className="text-base">{block.content}</p>}
                        {block.type === "bullet" && (
                          <div className="flex items-start">
                            <span className="mr-2 mt-1.5">•</span>
                            <p>{block.content}</p>
                          </div>
                        )}
                        {block.type === "numbered" && (
                          <div className="flex items-start">
                            <span className="mr-2 font-medium">
                              {noteBlocks.filter((b) => b.type === "numbered").indexOf(block) + 1}.
                            </span>
                            <p>{block.content}</p>
                          </div>
                        )}
                        {block.type === "code" && (
                          <pre className="bg-muted p-4 rounded-md overflow-x-auto">
                            <code>{block.content}</code>
                          </pre>
                        )}
                        {block.type === "divider" && <Separator className="my-4" />}
                      </div>
                    ))}
                  </div>
                )}
              </div>
            ) : (
              <div className="h-full flex items-center justify-center">
                <div className="text-center">
                  <FileText className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-xl font-medium">No note selected</h3>
                  <p className="text-muted-foreground mt-2">Select a note from the sidebar or create a new one</p>
                  <Button className="mt-4" onClick={handleCreateNewNote}>
                    <Plus className="h-4 w-4 mr-2" />
                    Create New Note
                  </Button>
                </div>
              </div>
            )}
          </div>

          {/* AI Assistant Panel */}
          <div
            className={cn(
              "border-l border-border transition-all duration-300 bg-card",
              isAiAssistantOpen ? "w-80" : "w-0 overflow-hidden",
            )}
          >
            <div className="h-full flex flex-col">
              <div className="p-3 border-b border-border flex justify-between items-center">
                <div className="flex items-center">
                  <Sparkles className="h-4 w-4 mr-2 text-primary" />
                  <h3 className="font-medium">AI Assistant</h3>
                </div>
                <Button variant="ghost" size="icon" onClick={toggleAiAssistant}>
                  <X className="h-4 w-4" />
                </Button>
              </div>

              <ScrollArea className="flex-1 p-4">
                <div className="space-y-4">
                  <div className="flex justify-start">
                    <div className="max-w-[80%] rounded-lg p-3 bg-muted">
                      <div className="flex items-center mb-1">
                        <Sparkles className="h-3 w-3 mr-1 text-primary" />
                        <span className="text-xs font-medium">AI Assistant</span>
                      </div>
                      <div>Hello! I'm your AI assistant. How can I help with your notes today?</div>
                    </div>
                  </div>
                </div>
              </ScrollArea>

              <div className="p-3 border-t border-border">
                <div className="mb-3">
                  <p className="text-xs text-muted-foreground mb-2">Suggested prompts:</p>
                  <div className="flex flex-wrap gap-2">
                    <Button variant="outline" size="sm" className="text-xs h-7">
                      Summarize this note
                    </Button>
                    <Button variant="outline" size="sm" className="text-xs h-7">
                      Improve the language
                    </Button>
                    <Button variant="outline" size="sm" className="text-xs h-7">
                      Add technical requirements
                    </Button>
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  <Input placeholder="Ask the AI assistant..." className="flex-1" />
                  <Button size="icon">
                    <Send className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="border-t border-border bg-yellow-100">
          <NotesFooter
            activeNote={activeNote}
            notes={notes}
            isAiAssistantOpen={isAiAssistantOpen}
            toggleAiAssistant={toggleAiAssistant}
            onSelectNote={handleNoteSelect}
          />
        </div>
      </div>
    </div>
  )
}


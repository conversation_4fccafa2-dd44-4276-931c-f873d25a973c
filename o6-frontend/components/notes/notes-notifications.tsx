"use client"

import { useState } from "react"
import { <PERSON>, <PERSON>, <PERSON>, X } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON>rollArea } from "@/components/ui/scroll-area"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { cn } from "@/lib/utils"

type Notification = {
  id: string
  title: string
  description: string
  time: Date
  read: boolean
  type: "reminder" | "mention" | "share" | "system"
}

interface NotificationsProps {
  onClose: () => void
}

export function NotesNotifications({ onClose }: NotificationsProps) {
  // Mock notifications data
  const [notifications, setNotifications] = useState<Notification[]>([
    {
      id: "1",
      title: "Reminder: Python Developer JD",
      description: "You set a reminder to review this note today",
      time: new Date(Date.now() - 1000 * 60 * 30), // 30 minutes ago
      read: false,
      type: "reminder",
    },
    {
      id: "2",
      title: "New comment on 'Q1 Sales Strategy'",
      description: "<PERSON> commented: 'Let's discuss this in our next meeting'",
      time: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2 hours ago
      read: false,
      type: "mention",
    },
    {
      id: "3",
      title: "Note shared with you",
      description: "Sarah shared 'Marketing Campaign Ideas' with you",
      time: new Date(Date.now() - 1000 * 60 * 60 * 24), // 1 day ago
      read: true,
      type: "share",
    },
    {
      id: "4",
      title: "Weekly summary available",
      description: "Your notes activity summary for last week is ready to view",
      time: new Date(Date.now() - 1000 * 60 * 60 * 24 * 2), // 2 days ago
      read: true,
      type: "system",
    },
  ])

  const markAsRead = (id: string) => {
    setNotifications(
      notifications.map((notification) => (notification.id === id ? { ...notification, read: true } : notification)),
    )
  }

  const markAllAsRead = () => {
    setNotifications(notifications.map((notification) => ({ ...notification, read: true })))
  }

  const deleteNotification = (id: string) => {
    setNotifications(notifications.filter((notification) => notification.id !== id))
  }

  const formatTime = (date: Date) => {
    const now = new Date()
    const diffMs = now.getTime() - date.getTime()
    const diffMins = Math.floor(diffMs / (1000 * 60))
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))

    if (diffMins < 60) {
      return `${diffMins}m ago`
    } else if (diffHours < 24) {
      return `${diffHours}h ago`
    } else if (diffDays < 7) {
      return `${diffDays}d ago`
    } else {
      return date.toLocaleDateString()
    }
  }

  const unreadCount = notifications.filter((n) => !n.read).length

  return (
    <Card className="w-[380px] shadow-lg">
      <CardHeader className="pb-3">
        <div className="flex justify-between items-center">
          <div>
            <CardTitle>Notifications</CardTitle>
            <CardDescription>Stay updated on your notes activity</CardDescription>
          </div>
          <Button variant="ghost" size="icon" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>
      <Tabs defaultValue="all">
        <div className="px-4 flex justify-between items-center">
          <TabsList>
            <TabsTrigger value="all">All</TabsTrigger>
            <TabsTrigger value="unread">
              Unread
              {unreadCount > 0 && (
                <span className="ml-1 text-xs bg-primary text-primary-foreground rounded-full px-1.5 py-0.5">
                  {unreadCount}
                </span>
              )}
            </TabsTrigger>
          </TabsList>
          <Button variant="ghost" size="sm" onClick={markAllAsRead} className="text-xs">
            Mark all as read
          </Button>
        </div>
        <CardContent className="pt-3">
          <ScrollArea className="h-[300px] pr-4">
            <TabsContent value="all" className="m-0">
              {notifications.length > 0 ? (
                <div className="space-y-3">
                  {notifications.map((notification) => (
                    <NotificationItem
                      key={notification.id}
                      notification={notification}
                      onMarkAsRead={markAsRead}
                      onDelete={deleteNotification}
                      formatTime={formatTime}
                    />
                  ))}
                </div>
              ) : (
                <div className="flex flex-col items-center justify-center h-[200px] text-center">
                  <Bell className="h-8 w-8 text-muted-foreground mb-2" />
                  <p className="text-muted-foreground">No notifications</p>
                </div>
              )}
            </TabsContent>
            <TabsContent value="unread" className="m-0">
              {notifications.filter((n) => !n.read).length > 0 ? (
                <div className="space-y-3">
                  {notifications
                    .filter((n) => !n.read)
                    .map((notification) => (
                      <NotificationItem
                        key={notification.id}
                        notification={notification}
                        onMarkAsRead={markAsRead}
                        onDelete={deleteNotification}
                        formatTime={formatTime}
                      />
                    ))}
                </div>
              ) : (
                <div className="flex flex-col items-center justify-center h-[200px] text-center">
                  <Check className="h-8 w-8 text-muted-foreground mb-2" />
                  <p className="text-muted-foreground">No unread notifications</p>
                </div>
              )}
            </TabsContent>
          </ScrollArea>
        </CardContent>
      </Tabs>
    </Card>
  )
}

interface NotificationItemProps {
  notification: Notification
  onMarkAsRead: (id: string) => void
  onDelete: (id: string) => void
  formatTime: (date: Date) => string
}

function NotificationItem({ notification, onMarkAsRead, onDelete, formatTime }: NotificationItemProps) {
  return (
    <div className={cn("p-3 rounded-lg border flex items-start gap-3", !notification.read && "bg-muted/50")}>
      <div className="mt-0.5">
        {notification.type === "reminder" && <Clock className="h-5 w-5 text-amber-500" />}
        {notification.type === "mention" && <Bell className="h-5 w-5 text-blue-500" />}
        {notification.type === "share" && <Bell className="h-5 w-5 text-green-500" />}
        {notification.type === "system" && <Bell className="h-5 w-5 text-purple-500" />}
      </div>
      <div className="flex-1 min-w-0">
        <div className="flex justify-between items-start">
          <h4 className="font-medium text-sm">{notification.title}</h4>
          <span className="text-xs text-muted-foreground whitespace-nowrap ml-2">{formatTime(notification.time)}</span>
        </div>
        <p className="text-sm text-muted-foreground mt-1 break-words">{notification.description}</p>
      </div>
      <div className="flex flex-col gap-1">
        {!notification.read && (
          <Button variant="ghost" size="icon" className="h-6 w-6" onClick={() => onMarkAsRead(notification.id)}>
            <Check className="h-3 w-3" />
          </Button>
        )}
        <Button variant="ghost" size="icon" className="h-6 w-6" onClick={() => onDelete(notification.id)}>
          <X className="h-3 w-3" />
        </Button>
      </div>
    </div>
  )
}


"use client"

import { Copy, Download, Edit, History, Menu, MoreHorizontal, Save, Share2, Undo } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import type { Note } from "./notes-module"

interface NotesHeaderProps {
  toggleSidebar: () => void
  sidebarOpen: boolean
  activeNote: Note | null
  editMode: boolean
  setEditMode: (value: boolean) => void
  onSave: () => void
}

export function NotesHeader({
  toggleSidebar,
  sidebarOpen,
  activeNote,
  editMode,
  setEditMode,
  onSave,
}: NotesHeaderProps) {
  return (
    <header className="border-b border-border p-2 flex justify-between items-center bg-card">
      <div className="flex items-center">
        <Button variant="ghost" size="icon" onClick={toggleSidebar}>
          <Menu className="h-5 w-5" />
        </Button>
        {activeNote && (
          <div className="ml-2 flex items-center">
            <h1 className="font-medium truncate max-w-md">{activeNote.title}</h1>
            <span className="text-xs text-muted-foreground ml-2">
              Last updated: {activeNote.updatedAt.toLocaleDateString()}
            </span>
          </div>
        )}
      </div>

      {activeNote && (
        <div className="flex items-center space-x-1">
          {editMode ? (
            <>
              <Button variant="outline" size="sm" onClick={() => setEditMode(false)}>
                Cancel
              </Button>
              <Button size="sm" onClick={onSave}>
                <Save className="h-4 w-4 mr-1" />
                Save
              </Button>
            </>
          ) : (
            <>
              <Button variant="outline" size="sm" onClick={() => setEditMode(true)}>
                <Edit className="h-4 w-4 mr-1" />
                Edit
              </Button>

              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="icon">
                    <MoreHorizontal className="h-5 w-5" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem>
                    <Copy className="h-4 w-4 mr-2" />
                    Copy
                  </DropdownMenuItem>
                  <DropdownMenuItem>
                    <Download className="h-4 w-4 mr-2" />
                    Download
                  </DropdownMenuItem>
                  <DropdownMenuItem>
                    <Share2 className="h-4 w-4 mr-2" />
                    Share
                  </DropdownMenuItem>
                  <DropdownMenuItem>
                    <History className="h-4 w-4 mr-2" />
                    Version History
                  </DropdownMenuItem>
                  <DropdownMenuItem>
                    <Undo className="h-4 w-4 mr-2" />
                    Revert Changes
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </>
          )}
        </div>
      )}
    </header>
  )
}


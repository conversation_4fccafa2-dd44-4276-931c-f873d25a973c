import type React from "react"
import { WorkspaceModule } from "@/components/workspace/workspace-module"
import { redirect } from "next/navigation"
import { auth } from "@/app/auth"

export default async function CRMLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  // Check authentication server-side for stronger protection
  const session = await auth()
  
  // If no session exists, redirect to login
  if (!session) {
    redirect('/login')
  }
  
  return (
    <div className="h-screen flex flex-col">
      <WorkspaceModule title="O6" module="crm" user={session.user}>
        {children}
      </WorkspaceModule>
    </div>
  )
}


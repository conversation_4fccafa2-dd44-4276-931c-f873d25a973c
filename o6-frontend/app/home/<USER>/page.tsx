"use client"

import { useSession } from "next-auth/react"
import { useRouter } from "next/navigation"
import { useEffect } from "react"
import { HomeDashboardView } from "@/components/workspace/views/home-dashboard-view"
import { Skeleton } from "@/components/ui/skeleton"

export default function HomeDashboardPage() {
  const { data: session, status } = useSession()
  const router = useRouter()

  useEffect(() => {
    // If the user is not authenticated, redirect to login
    if (status === "unauthenticated") {
      router.push("/login")
    }
  }, [status, router])

  if (status === "loading") {
    return (
      <div className="flex flex-col space-y-4 p-8">
        <Skeleton className="h-8 w-[250px]" />
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {[1, 2, 3].map((i) => (
            <Skeleton key={i} className="h-[180px] w-full rounded-xl" />
          ))}
        </div>
        <Skeleton className="h-[300px] w-full mt-4" />
      </div>
    )
  }

  if (!session) {
    return null
  }

  return <HomeDashboardView />
}


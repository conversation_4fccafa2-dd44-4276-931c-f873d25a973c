import { redirect } from "next/navigation";
import { auth } from "@/app/auth";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { getCloudflareContext } from "@opennextjs/cloudflare";

export default async function ProfilePage() {
  // Get the session and user
  const session = await auth();
  
  // If no session exists, redirect to login
  if (!session) {
    redirect("/login");
  }
  
  const user = session.user;
    // Define organization type
  interface Organization {
    id: string;
    name: string;
    industry?: string;
    size?: string;
    created_at: string;
    updated_at: string;
  }
  
  // Get organization details if available
  let organization: Organization | null = null;
  if (user && user.organizationId) {
    try {
      const context = await getCloudflareContext({ async: true });
      const db = context.env.DB;
      
      const result = await db.prepare(
        `SELECT * FROM organizations WHERE id = ?`
      ).bind(user.organizationId).first<Organization>();
      
      if (result) {
        organization = result;
      }
    } catch (error) {
      console.error("Error fetching organization:", error);
    }
  }
  return (
    <div className="container max-w-4xl mx-auto py-8 px-4">
      <div className="mb-8">
        <h1 className="text-3xl font-bold">Profile</h1>
        <p className="text-gray-500">Manage your account settings and preferences</p>
      </div>
      
      <div className="grid gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Personal Information</CardTitle>
            <CardDescription>Update your personal information and how we can contact you</CardDescription>
          </CardHeader>
          <form>
            <CardContent className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="name">Name</Label>
                <Input id="name" defaultValue={user?.name || ""} />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="email">Email address</Label>
                <Input id="email" type="email" defaultValue={user?.email || ""} readOnly />
                <p className="text-sm text-gray-500">
                  Your email address is used for authentication and cannot be changed directly.
                </p>
              </div>
            </CardContent>
            <CardFooter>
              <Button type="submit" className="bg-forest-600 hover:bg-forest-700 text-white">
                Save changes
              </Button>
            </CardFooter>
          </form>
        </Card>
        
        {organization && (
          <Card>
            <CardHeader>
              <CardTitle>Organization</CardTitle>
              <CardDescription>Your organization details</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <p className="font-medium">{organization.name}</p>
                {organization.industry && <p className="text-sm text-gray-500">Industry: {organization.industry}</p>}
                {organization.size && <p className="text-sm text-gray-500">Size: {organization.size}</p>}
              </div>
            </CardContent>
          </Card>
        )}
        
        <Card>
          <CardHeader>
            <CardTitle>Security</CardTitle>
            <CardDescription>Manage your account security</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h3 className="text-base font-medium">Magic link authentication</h3>
              <p className="text-sm text-gray-500">
                You are using passwordless authentication with magic links sent to your email.
              </p>
            </div>
            
            <div>
              <h3 className="text-base font-medium">Active sessions</h3>
              <p className="text-sm text-gray-500">
                You can view and manage your active sessions from the settings page.
              </p>
              <div className="mt-2">
                <Button
                  variant="outline"
                  className="text-red-600 hover:text-red-700 hover:bg-red-50"
                  onClick={async () => {
                    "use server";
                    // This will be handled by the client component wrapping this button
                  }}
                >
                  Sign out of all sessions
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

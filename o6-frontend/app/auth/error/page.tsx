"use client"

import Link from "next/link"
import { <PERSON>, <PERSON><PERSON>ontent, CardDescription, <PERSON><PERSON>oot<PERSON>, CardHeader, CardTitle } from "@/components/ui/card"
import { AlertCircle, ArrowLeft } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { useSearchParams } from "next/navigation"

export default function AuthErrorPage() {
  const searchParams = useSearchParams();
  const error = searchParams.get("error");
    let errorTitle = "Authentication Error";
  let errorMessage = "An error occurred during the authentication process. Please try again.";
  let showRegisterLink = false;
  
  // Handle specific error cases
  switch (error) {
    case "Verification":
      errorTitle = "Verification Link Expired";
      errorMessage = "Your verification link has expired or has already been used. Please request a new one.";
      break;
    case "AccessDenied":
      errorTitle = "Access Denied";
      errorMessage = "You don't have permission to access this resource.";
      break;
    case "Configuration":
      errorTitle = "Server Configuration Error";
      errorMessage = "There's an issue with the server configuration. Please contact support.";
      break;
    case "EmailSignin":
      errorTitle = "Email Authentication Failed";
      errorMessage = "The email verification link is invalid or has expired.";
      break;
    case "UserNotFound":
      errorTitle = "User Not Found";
      errorMessage = "No account found with this email. You need to register first.";
      showRegisterLink = true;
      break;
    // Add more error cases as needed
  }
  
  return (
    <div className="min-h-screen bg-cream-100 flex flex-col">
      {/* Header */}
      <header className="border-b border-gray-200/50 bg-white">
        <div className="container mx-auto px-4 py-4">
          <Link href="/" className="flex items-center">
            <ArrowLeft className="h-4 w-4 mr-2" />
            <div className="text-forest-600 font-bold text-xl">O6</div>
          </Link>
        </div>
      </header>

      {/* Main Content */}
      <div className="flex-1 flex items-center justify-center p-4">
        <div className="w-full max-w-md">
          <Card className="border-gray-200/50 shadow-sm">
            <CardHeader className="text-center">
              <div className="mx-auto bg-red-100 w-16 h-16 rounded-full flex items-center justify-center mb-4">
                <AlertCircle className="h-8 w-8 text-red-600" />
              </div>
              <CardTitle className="text-2xl">{errorTitle}</CardTitle>
              <CardDescription>
                {errorMessage}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-center text-sm text-gray-600">
                Please try again or contact support if this issue persists.
              </p>
            </CardContent>            <CardFooter className="flex flex-col space-y-2">
              <Link href="/login" className="w-full">
                <Button className="bg-forest-600 hover:bg-forest-700 text-white w-full">
                  Back to Login
                </Button>
              </Link>
              {showRegisterLink && (
                <Link href="/register" className="w-full">
                  <Button variant="outline" className="w-full border-gray-200">
                    Create an Account
                  </Button>
                </Link>
              )}
            </CardFooter>
          </Card>
        </div>
      </div>
    </div>
  )
}

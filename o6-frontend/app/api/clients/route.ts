// app/api/clients/route.ts
import { auth } from "@/app/auth";
import { NextResponse } from 'next/server';
import { getCloudflareContext } from "@opennextjs/cloudflare";
import { createClient } from '@supabase/supabase-js';

export async function GET(request: Request) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get Supabase access
    const context = await getCloudflareContext({ async: true });
    if (!context.env.SUPABASE_URL || !context.env.SUPABASE_SERVICE_ROLE_KEY) {
      throw new Error("Supabase URL or Service Role Key is not configured.");
    }
    
    const supabase = createClient(context.env.SUPABASE_URL, context.env.SUPABASE_SERVICE_ROLE_KEY);

    // Get user's organization
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('org_id')
      .eq('id', session.user.id)
      .single();

    if (userError || !userData) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Fetch clients for the user's organization
    const { data: clients, error } = await supabase
      .from('clients')
      .select('*')
      .eq('org_id', userData.org_id)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching clients:', error);
      return NextResponse.json({ error: "Failed to fetch clients" }, { status: 500 });
    }

    // Transform database data to match frontend types
    const transformedClients = clients.map(client => ({
      id: client.id,
      orgId: client.org_id,
      name: client.name,
      industry: client.industry || '',
      location: client.location || '',
      contactPerson: client.contact_person || '',
      contactEmail: client.contact_email || '',
      contactPhone: client.contact_phone || '',
      status: client.status,
      notes: client.notes || '',
      openJobs: client.open_jobs || 0,
      totalPlacements: client.total_placements || 0,
      createdAt: new Date(client.created_at),
      updatedAt: new Date(client.updated_at)
    }));

    return NextResponse.json(transformedClients);
  } catch (error) {
    console.error('Failed to fetch clients:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return NextResponse.json({ error: errorMessage }, { status: 500 });
  }
}

export async function POST(request: Request) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json() as {
      name: string;
      industry?: string;
      location?: string;
      contactPerson?: string;
      contactEmail?: string;
      contactPhone?: string;
      status?: string;
      notes?: string;
    };
    const { name, industry, location, contactPerson, contactEmail, contactPhone, status, notes } = body;

    // Validate required fields
    if (!name) {
      return NextResponse.json({ error: "Client name is required" }, { status: 400 });
    }

    // Get Supabase access
    const context = await getCloudflareContext({ async: true });
    if (!context.env.SUPABASE_URL || !context.env.SUPABASE_SERVICE_ROLE_KEY) {
      throw new Error("Supabase URL or Service Role Key is not configured.");
    }
    
    const supabase = createClient(context.env.SUPABASE_URL, context.env.SUPABASE_SERVICE_ROLE_KEY);

    // Get user's organization
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('org_id')
      .eq('id', session.user.id)
      .single();

    if (userError || !userData) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Create client
    const { data: client, error } = await supabase
      .from('clients')
      .insert([{
        org_id: userData.org_id,
        name,
        industry: industry || '',
        location: location || '',
        contact_person: contactPerson || '',
        contact_email: contactEmail || '',
        contact_phone: contactPhone || '',
        status: status || 'lead',
        notes: notes || '',
        open_jobs: 0,
        total_placements: 0
      }])
      .select()
      .single();

    if (error) {
      console.error('Error creating client:', error);
      return NextResponse.json({ error: "Failed to create client" }, { status: 500 });
    }

    // Transform database data to match frontend types
    const transformedClient = {
      id: client.id,
      orgId: client.org_id,
      name: client.name,
      industry: client.industry || '',
      location: client.location || '',
      contactPerson: client.contact_person || '',
      contactEmail: client.contact_email || '',
      contactPhone: client.contact_phone || '',
      status: client.status,
      notes: client.notes || '',
      openJobs: client.open_jobs || 0,
      totalPlacements: client.total_placements || 0,
      createdAt: new Date(client.created_at),
      updatedAt: new Date(client.updated_at)
    };

    return NextResponse.json(transformedClient, { status: 201 });
  } catch (error) {
    console.error('Failed to create client:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return NextResponse.json({ error: errorMessage }, { status: 500 });
  }
}

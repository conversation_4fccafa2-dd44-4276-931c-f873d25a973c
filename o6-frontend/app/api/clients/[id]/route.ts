import { NextRequest, NextResponse } from 'next/server'
import { auth } from "@/app/auth";
import { getCloudflareContext } from "@opennextjs/cloudflare";
import { createClient } from '@supabase/supabase-js';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const clientId = params.id

    // Get Supabase access
    const context = await getCloudflareContext({ async: true });
    if (!context.env.SUPABASE_URL || !context.env.SUPABASE_SERVICE_ROLE_KEY) {
      throw new Error("Supabase URL or Service Role Key is not configured.");
    }
    
    const supabase = createClient(context.env.SUPABASE_URL, context.env.SUPABASE_SERVICE_ROLE_KEY);

    // Get user's organization
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('org_id')
      .eq('id', session.user.id)
      .single();

    if (userError || !userData) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Get the client
    const { data: client, error } = await supabase
      .from('clients')
      .select('*')
      .eq('id', clientId)
      .eq('org_id', userData.org_id)
      .single();

    if (error) {
      console.error('Error fetching client:', error);
      return NextResponse.json({ error: "Client not found" }, { status: 404 });
    }

    // Transform database data to match frontend types
    const transformedClient = {
      id: client.id,
      orgId: client.org_id,
      name: client.name,
      industry: client.industry || '',
      location: client.location || '',
      contactPerson: client.contact_person || '',
      contactEmail: client.contact_email || '',
      contactPhone: client.contact_phone || '',
      status: client.status,
      notes: client.notes || '',
      openJobs: client.open_jobs || 0,
      totalPlacements: client.total_placements || 0,
      createdAt: new Date(client.created_at),
      updatedAt: new Date(client.updated_at)
    };
    
    return NextResponse.json(transformedClient)
  } catch (error) {
    console.error('Error fetching client:', error)
    return NextResponse.json(
      { error: 'Failed to fetch client' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const clientId = params.id
    const body = await request.json() as {
      name?: string;
      industry?: string;
      location?: string;
      contactPerson?: string;
      contactEmail?: string;
      contactPhone?: string;
      status?: string;
      notes?: string;
    };

    // Get Supabase access
    const context = await getCloudflareContext({ async: true });
    if (!context.env.SUPABASE_URL || !context.env.SUPABASE_SERVICE_ROLE_KEY) {
      throw new Error("Supabase URL or Service Role Key is not configured.");
    }
    
    const supabase = createClient(context.env.SUPABASE_URL, context.env.SUPABASE_SERVICE_ROLE_KEY);

    // Get user's organization
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('org_id')
      .eq('id', session.user.id)
      .single();

    if (userError || !userData) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Update the client
    const { data: client, error } = await supabase
      .from('clients')
      .update({
        name: body.name,
        industry: body.industry,
        location: body.location,
        contact_person: body.contactPerson,
        contact_email: body.contactEmail,
        contact_phone: body.contactPhone,
        status: body.status,
        notes: body.notes,
      })
      .eq('id', clientId)
      .eq('org_id', userData.org_id)
      .select()
      .single();

    if (error) {
      console.error('Error updating client:', error);
      return NextResponse.json({ error: "Failed to update client" }, { status: 500 });
    }

    // Transform database data to match frontend types
    const transformedClient = {
      id: client.id,
      orgId: client.org_id,
      name: client.name,
      industry: client.industry || '',
      location: client.location || '',
      contactPerson: client.contact_person || '',
      contactEmail: client.contact_email || '',
      contactPhone: client.contact_phone || '',
      status: client.status,
      notes: client.notes || '',
      openJobs: client.open_jobs || 0,
      totalPlacements: client.total_placements || 0,
      createdAt: new Date(client.created_at),
      updatedAt: new Date(client.updated_at)
    };
    
    return NextResponse.json(transformedClient)
  } catch (error) {
    console.error('Error updating client:', error)
    return NextResponse.json(
      { error: 'Failed to update client' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const clientId = params.id

    // Get Supabase access
    const context = await getCloudflareContext({ async: true });
    if (!context.env.SUPABASE_URL || !context.env.SUPABASE_SERVICE_ROLE_KEY) {
      throw new Error("Supabase URL or Service Role Key is not configured.");
    }
    
    const supabase = createClient(context.env.SUPABASE_URL, context.env.SUPABASE_SERVICE_ROLE_KEY);

    // Get user's organization
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('org_id')
      .eq('id', session.user.id)
      .single();

    if (userError || !userData) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Delete the client
    const { error } = await supabase
      .from('clients')
      .delete()
      .eq('id', clientId)
      .eq('org_id', userData.org_id);

    if (error) {
      console.error('Error deleting client:', error);
      return NextResponse.json({ error: "Failed to delete client" }, { status: 500 });
    }
    
    return NextResponse.json({ message: 'Client deleted successfully' })
  } catch (error) {
    console.error('Error deleting client:', error)
    return NextResponse.json(
      { error: 'Failed to delete client' },
      { status: 500 }
    )
  }
}

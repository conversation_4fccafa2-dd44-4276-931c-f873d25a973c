import { NextResponse } from "next/server";
import { getCloudflareContext } from "@opennextjs/cloudflare";
import { SupabaseAdapter } from "@auth/supabase-adapter";

export async function POST(request: Request) {
  try {
    const { email } = await request.json();

    if (!email) {
      return NextResponse.json(
        { error: "Email is required" },
        { status: 400 }
      );
    }
    // Get Supabase access
    const context = await getCloudflareContext({ async: true });
    
    // Check if user exists
    const adapter = SupabaseAdapter({
      url: context.env.SUPABASE_URL,
      secret: context.env.SUPABASE_SERVICE_ROLE_KEY,
    });
    const existingUser = await adapter.getUserByEmail?.(email);

    return NextResponse.json({
      exists: !!existingUser,
      pendingVerification: existingUser && existingUser.emailVerified === null,
      user: existingUser ? {
        email: existingUser.email,
        name: existingUser.name,
        emailVerified: existingUser.emailVerified
      } : null
    });
  } catch (error) {
    console.error("Error checking user:", error);
    return NextResponse.json(
      { error: "Failed to check user" },
      { status: 500 }
    );
  }
}

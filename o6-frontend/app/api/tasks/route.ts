// app/api/tasks/route.ts
import { auth } from "@/app/auth";
import { NextResponse } from 'next/server';
import { getCloudflareContext } from "@opennextjs/cloudflare";
import { createClient } from '@supabase/supabase-js';

export async function GET(request: Request) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const objectiveId = searchParams.get('objectiveId');
    const persona = searchParams.get('persona');

    // Get Supabase access
    const context = await getCloudflareContext({ async: true });
    if (!context.env.SUPABASE_URL || !context.env.SUPABASE_SERVICE_ROLE_KEY) {
      throw new Error("Supabase URL or Service Role Key is not configured.");
    }
    
    const supabase = createClient(context.env.SUPABASE_URL, context.env.SUPABASE_SERVICE_ROLE_KEY);

    // Get user's organization
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('org_id')
      .eq('id', session.user.id)
      .single();

    if (userError || !userData) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Build query - join with objectives and programs to ensure tasks belong to user's org
    let query = supabase
      .from('tasks')
      .select(`
        *,
        objectives!inner(
          program_id,
          programs!inner(org_id)
        )
      `)
      .eq('objectives.programs.org_id', userData.org_id)
      .order('created_at', { ascending: false });

    // Filter by objectiveId if provided
    if (objectiveId) {
      query = query.eq('objective_id', objectiveId);
    }

    // Filter by persona if provided
    if (persona) {
      query = query.eq('persona', persona);
    }

    const { data: tasks, error } = await query;

    if (error) {
      console.error('Error fetching tasks:', error);
      return NextResponse.json({ error: "Failed to fetch tasks" }, { status: 500 });
    }

    // Transform database data to match frontend types
    const transformedTasks = tasks.map(task => ({
      id: task.id,
      objectiveId: task.objective_id,
      title: task.title,
      description: task.description,
      dueDate: task.due_date ? new Date(task.due_date) : new Date(),
      status: task.status,
      priority: task.priority,
      assignedTo: task.assigned_to,
      persona: task.persona,
      type: task.type,
      noteId: task.note_id,
      relatedItems: task.related_items || []
    }));

    return NextResponse.json(transformedTasks);
  } catch (error) {
    console.error('Failed to fetch tasks:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return NextResponse.json({ error: errorMessage }, { status: 500 });
  }
}

export async function POST(request: Request) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const { objectiveId, title, description, dueDate, status, priority, assignedTo, persona, type, relatedItems } = body;

    // Validate required fields
    if (!objectiveId || !title || !assignedTo || !persona) {
      return NextResponse.json({ error: "Missing required fields" }, { status: 400 });
    }

    // Get Supabase access
    const context = await getCloudflareContext({ async: true });
    if (!context.env.SUPABASE_URL || !context.env.SUPABASE_SERVICE_ROLE_KEY) {
      throw new Error("Supabase URL or Service Role Key is not configured.");
    }
    
    const supabase = createClient(context.env.SUPABASE_URL, context.env.SUPABASE_SERVICE_ROLE_KEY);

    // Get user's organization
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('org_id')
      .eq('id', session.user.id)
      .single();

    if (userError || !userData) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Verify objective belongs to user's organization
    const { data: objectiveData, error: objectiveError } = await supabase
      .from('objectives')
      .select(`
        id,
        programs!inner(org_id)
      `)
      .eq('id', objectiveId)
      .eq('programs.org_id', userData.org_id)
      .single();

    if (objectiveError || !objectiveData) {
      return NextResponse.json({ error: "Objective not found or access denied" }, { status: 404 });
    }

    // Create task
    const { data: task, error } = await supabase
      .from('tasks')
      .insert([{
        objective_id: objectiveId,
        title,
        description,
        due_date: dueDate ? new Date(dueDate).toISOString().split('T')[0] : null,
        status: status || 'pending',
        priority: priority || 'medium',
        assigned_to: assignedTo,
        persona,
        type,
        related_items: relatedItems || []
      }])
      .select()
      .single();

    if (error) {
      console.error('Error creating task:', error);
      return NextResponse.json({ error: "Failed to create task" }, { status: 500 });
    }

    // Transform database data to match frontend types
    const transformedTask = {
      id: task.id,
      objectiveId: task.objective_id,
      title: task.title,
      description: task.description,
      dueDate: task.due_date ? new Date(task.due_date) : new Date(),
      status: task.status,
      priority: task.priority,
      assignedTo: task.assigned_to,
      persona: task.persona,
      type: task.type,
      noteId: task.note_id,
      relatedItems: task.related_items || []
    };

    return NextResponse.json(transformedTask, { status: 201 });
  } catch (error) {
    console.error('Failed to create task:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return NextResponse.json({ error: errorMessage }, { status: 500 });
  }
}

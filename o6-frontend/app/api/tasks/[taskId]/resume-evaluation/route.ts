// app/api/tasks/[taskId]/resume-evaluation/route.ts
import { auth } from "@/app/auth";
import { NextResponse } from 'next/server';
import { getCloudflareContext } from "@opennextjs/cloudflare";
import { createClient } from '@supabase/supabase-js';

const HR_API_URL = process.env.HR_ASSISTANT_API_URL || 'http://localhost:8000/api/hr-assistant-unified';

export async function POST(
  request: Request,
  { params }: { params: Promise<{ taskId: string }> }
) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { taskId } = await params;

    // Parse form data
    const formData = await request.formData();
    const jobDescription = formData.get('job_description') as string;
    const resumeFile = formData.get('resume_file') as File;

    if (!jobDescription || !resumeFile) {
      return NextResponse.json(
        { error: "Both job_description and resume_file are required" },
        { status: 400 }
      );
    }

    // Get Supabase access
    const context = await getCloudflareContext({ async: true });
    if (!context.env.SUPABASE_URL || !context.env.SUPABASE_SERVICE_ROLE_KEY) {
      throw new Error("Supabase URL or Service Role Key is not configured.");
    }
    
    const supabase = createClient(context.env.SUPABASE_URL, context.env.SUPABASE_SERVICE_ROLE_KEY);

    // Verify user has access to this task
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('org_id')
      .eq('id', session.user.id)
      .single();

    if (userError || !userData) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Verify task belongs to user's organization
    const { data: taskData, error: taskError } = await supabase
      .from('tasks')
      .select(`
        *,
        objectives!inner(
          program_id,
          programs!inner(org_id)
        )
      `)
      .eq('id', taskId)
      .eq('objectives.programs.org_id', userData.org_id)
      .single();

    if (taskError || !taskData) {
      return NextResponse.json({ error: "Task not found or access denied" }, { status: 404 });
    }

    // Convert file to text (basic text extraction for PDFs and text files)
    let resumeText: string;
    try {
      if (resumeFile.type === 'application/pdf') {
        // For PDF files, we'll send the raw file to the external API
        // and extract text from the response or use a basic approach
        resumeText = `PDF file: ${resumeFile.name}`;
      } else {
        // For text files, read directly
        resumeText = await resumeFile.text();
      }
    } catch (error) {
      console.error('Error reading file:', error);
      return NextResponse.json(
        { error: "Failed to read resume file" },
        { status: 400 }
      );
    }

    // Call the external HR assistant API
    const hrApiFormData = new FormData();
    hrApiFormData.append('job_description', jobDescription);
    hrApiFormData.append('resume_file', resumeFile);

    let hrApiResponse;
    try {
      hrApiResponse = await fetch(HR_API_URL, {
        method: 'POST',
        body: hrApiFormData,
      });
    } catch (error) {
      console.error('Failed to connect to HR assistant API:', error);
      return NextResponse.json(
        { error: "HR assistant service is currently unavailable. Please try again later." },
        { status: 503 }
      );
    }

    if (!hrApiResponse.ok) {
      const errorText = await hrApiResponse.text().catch(() => 'Unknown error');
      console.error('HR API response error:', hrApiResponse.status, errorText);
      return NextResponse.json(
        { error: `Failed to evaluate resume: ${hrApiResponse.status} ${hrApiResponse.statusText}` },
        { status: 500 }
      );
    }

    const hrApiResult = await hrApiResponse.json() as {
      intent: string;
      similarity_score: number;
      evaluation_json: {
        candidate_name: string;
        overall_score: number;
        skills_match: {
          score: number;
          present_skills: string[];
          missing_skills: string[];
          explanation: string;
        };
        experience_relevance: {
          score: number;
          explanation: string;
        };
        recommendations: string[];
      };
    };

    // Extract candidate name from evaluation result
    const candidateName = hrApiResult.evaluation_json?.candidate_name || 'Unknown';

    // Save to database
    const { data: savedData, error: saveError } = await supabase
      .from('resume_evaluations')
      .insert([
        {
          task_id: taskId,
          candidate_name: candidateName,
          resume_text: resumeText,
          job_description: jobDescription,
          similarity_score: hrApiResult.similarity_score,
          evaluation_result: hrApiResult.evaluation_json,
        }
      ])
      .select()
      .single();

    if (saveError) {
      console.error('Error saving resume evaluation:', saveError);
      return NextResponse.json(
        { error: "Failed to save evaluation result" },
        { status: 500 }
      );
    }

    // Return the complete response including the saved data
    return NextResponse.json({
      intent: hrApiResult.intent,
      similarity_score: hrApiResult.similarity_score,
      evaluation_json: hrApiResult.evaluation_json,
      save_status: {
        data: [savedData],
        count: 1
      }
    });

  } catch (error) {
    console.error('Resume evaluation error:', error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function GET(
  request: Request,
  { params }: { params: Promise<{ taskId: string }> }
) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { taskId } = await params;

    // Get Supabase access
    const context = await getCloudflareContext({ async: true });
    if (!context.env.SUPABASE_URL || !context.env.SUPABASE_SERVICE_ROLE_KEY) {
      throw new Error("Supabase URL or Service Role Key is not configured.");
    }
    
    const supabase = createClient(context.env.SUPABASE_URL, context.env.SUPABASE_SERVICE_ROLE_KEY);

    // Verify user has access to this task
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('org_id')
      .eq('id', session.user.id)
      .single();

    if (userError || !userData) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Verify task belongs to user's organization and get resume evaluations
    const { data: evaluations, error: evaluationsError } = await supabase
      .from('resume_evaluations')
      .select(`
        *,
        tasks!inner(
          *,
          objectives!inner(
            program_id,
            programs!inner(org_id)
          )
        )
      `)
      .eq('task_id', taskId)
      .eq('tasks.objectives.programs.org_id', userData.org_id)
      .order('created_at', { ascending: false });

    if (evaluationsError) {
      console.error('Error fetching resume evaluations:', evaluationsError);
      return NextResponse.json({ error: "Failed to fetch evaluations" }, { status: 500 });
    }

    return NextResponse.json({
      data: evaluations,
      count: evaluations.length
    });

  } catch (error) {
    console.error('Get resume evaluations error:', error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// app/api/jobs/route.ts
import { auth } from "@/app/auth";
import { NextResponse } from 'next/server';
import { getCloudflareContext } from "@opennextjs/cloudflare";
import { createClient } from '@supabase/supabase-js';

export async function GET(request: Request) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const clientId = searchParams.get('clientId');
    const persona = searchParams.get('persona');

    // Get Supabase access
    const context = await getCloudflareContext({ async: true });
    if (!context.env.SUPABASE_URL || !context.env.SUPABASE_SERVICE_ROLE_KEY) {
      throw new Error("Supabase URL or Service Role Key is not configured.");
    }
    
    const supabase = createClient(context.env.SUPABASE_URL, context.env.SUPABASE_SERVICE_ROLE_KEY);

    // Get user's organization
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('org_id')
      .eq('id', session.user.id)
      .single();

    if (userError || !userData) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Build query
    let query = supabase
      .from('jobs')
      .select('*')
      .eq('org_id', userData.org_id)
      .order('created_at', { ascending: false });

    // Filter by client if provided
    if (clientId) {
      query = query.eq('client_id', clientId);
    }

    // Filter by persona if provided
    if (persona) {
      query = query.eq('persona', persona);
    }

    const { data: jobs, error } = await query;

    if (error) {
      console.error('Error fetching jobs:', error);
      return NextResponse.json({ error: "Failed to fetch jobs" }, { status: 500 });
    }

    // Transform database data to match frontend types
    const transformedJobs = jobs.map(job => ({
      id: job.id,
      title: job.title,
      content: job.content || '',
      tags: job.tags || [],
      department: job.department || '',
      location: job.location || '',
      salary: job.salary || '',
      experience: job.experience || '',
      persona: job.persona,
      type: job.type,
      status: job.status,
      category: job.category || '',
      clientId: job.client_id,
      versions: job.versions || [],
      createdAt: new Date(job.created_at),
      updatedAt: new Date(job.updated_at)
    }));

    return NextResponse.json(transformedJobs);
  } catch (error) {
    console.error('Failed to fetch jobs:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return NextResponse.json({ error: errorMessage }, { status: 500 });
  }
}

export async function POST(request: Request) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json() as {
      title: string;
      content?: string;
      tags?: string[];
      department?: string;
      location?: string;
      salary?: string;
      experience?: string;
      persona: string;
      type?: string;
      status?: string;
      category?: string;
      clientId?: string;
    };

    const { 
      title, content, tags, department, location, salary, 
      experience, persona, type, status, category, clientId 
    } = body;

    // Validate required fields
    if (!title || !persona) {
      return NextResponse.json({ error: "Title and persona are required" }, { status: 400 });
    }

    // Get Supabase access
    const context = await getCloudflareContext({ async: true });
    if (!context.env.SUPABASE_URL || !context.env.SUPABASE_SERVICE_ROLE_KEY) {
      throw new Error("Supabase URL or Service Role Key is not configured.");
    }
    
    const supabase = createClient(context.env.SUPABASE_URL, context.env.SUPABASE_SERVICE_ROLE_KEY);

    // Get user's organization
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('org_id')
      .eq('id', session.user.id)
      .single();

    if (userError || !userData) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Create job
    const { data: job, error } = await supabase
      .from('jobs')
      .insert([{
        org_id: userData.org_id,
        client_id: clientId || null,
        title,
        content: content || '',
        tags: tags || [],
        department: department || '',
        location: location || '',
        salary: salary || '',
        experience: experience || '',
        persona,
        type: type || 'job_description',
        status: status || 'draft',
        category: category || '',
        versions: []
      }])
      .select()
      .single();

    if (error) {
      console.error('Error creating job:', error);
      return NextResponse.json({ error: "Failed to create job" }, { status: 500 });
    }

    // Transform database data to match frontend types
    const transformedJob = {
      id: job.id,
      title: job.title,
      content: job.content || '',
      tags: job.tags || [],
      department: job.department || '',
      location: job.location || '',
      salary: job.salary || '',
      experience: job.experience || '',
      persona: job.persona,
      type: job.type,
      status: job.status,
      category: job.category || '',
      clientId: job.client_id,
      versions: job.versions || [],
      createdAt: new Date(job.created_at),
      updatedAt: new Date(job.updated_at)
    };

    return NextResponse.json(transformedJob, { status: 201 });
  } catch (error) {
    console.error('Failed to create job:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return NextResponse.json({ error: errorMessage }, { status: 500 });
  }
}

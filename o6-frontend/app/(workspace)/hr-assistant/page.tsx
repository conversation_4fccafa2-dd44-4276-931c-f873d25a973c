"use client"

import React, { useState } from 'react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { ChatInterface } from '@/components/hr-assistant/chat-interface'
import { ResumeAnalyzer } from '@/components/hr-assistant/resume-analyzer'
import { Bot, FileText, Users, MessageSquare } from 'lucide-react'

export default function HRAssistantPage() {
  const [activeTab, setActiveTab] = useState('chat')

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="space-y-2">
        <div className="flex items-center gap-3">
          <Bot className="h-8 w-8 text-blue-600" />
          <div>
            <h1 className="text-3xl font-bold">HR Assistant</h1>
            <p className="text-gray-600">
              AI-powered recruiting and candidate evaluation tools
            </p>
          </div>
        </div>
        
        {/* Status Badge */}
        <div className="flex items-center gap-2">
          <Badge variant="outline" className="text-green-600 border-green-600">
            <div className="w-2 h-2 bg-green-600 rounded-full mr-2"></div>
            Connected to FastAPI Backend
          </Badge>
          <Badge variant="secondary">
            Backend: http://localhost:8080/api
          </Badge>
        </div>
      </div>

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="chat" className="flex items-center gap-2">
            <MessageSquare className="h-4 w-4" />
            Chat Assistant
          </TabsTrigger>
          <TabsTrigger value="resume" className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            Resume Analysis
          </TabsTrigger>
          <TabsTrigger value="evaluation" className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            Candidate Evaluation
          </TabsTrigger>
        </TabsList>

        {/* Chat Tab */}
        <TabsContent value="chat" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-2">
              <ChatInterface
                title="HR Assistant Chat"
                placeholder="Ask about recruiting, candidate evaluation, job descriptions, interview questions..."
                context="HR and recruiting assistance"
              />
            </div>
            
            <div className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Quick Actions</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="space-y-2">
                    <h4 className="font-medium text-sm">Sample Questions:</h4>
                    <div className="space-y-1 text-sm text-gray-600">
                      <p>• "Help me write a job description for a software engineer"</p>
                      <p>• "What interview questions should I ask for a marketing role?"</p>
                      <p>• "How do I evaluate technical skills in candidates?"</p>
                      <p>• "Create an onboarding checklist for new hires"</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Features</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2 text-sm">
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span>Resume Analysis</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span>Candidate Evaluation</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span>Job Description Writing</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span>Interview Questions</span>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>

        {/* Resume Analysis Tab */}
        <TabsContent value="resume" className="space-y-6">
          <ResumeAnalyzer
            onAnalysisComplete={(analysis) => {
              console.log('Resume analysis completed:', analysis)
            }}
          />
        </TabsContent>

        {/* Candidate Evaluation Tab */}
        <TabsContent value="evaluation" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Candidate Evaluation</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-12">
                <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Candidate Evaluation Tool
                </h3>
                <p className="text-gray-600 mb-4">
                  Comprehensive candidate assessment and scoring system
                </p>
                <Badge variant="secondary">Coming Soon</Badge>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

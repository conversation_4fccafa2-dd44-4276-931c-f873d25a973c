"use client"

import type React from "react"

import { useState } from "react"
import Link from "next/link"
import { useRout<PERSON> } from "next/navigation"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { ArrowLeft, CheckCircle, MailCheck } from "lucide-react"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { useAuth } from "@/hooks/use-auth"
import { signInWithResend } from "../../actions/auth"

export default function RegisterPage() {
  const router = useRouter()
  const [step, setStep] = useState<"account" | "organization" | "confirmation" | "verification">("account")
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [formData, setFormData] = useState({
    firstName: "",
    lastName: "",
    email: "",
    organizationName: "",
    industry: "",
    size: "",
  })

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleAccountSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    setStep("organization")
  }
  const handleOrganizationSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setError(null)
    
    try {
      // First, register the user's info
      const registerResponse = await fetch('/api/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      })
      
      if (!registerResponse.ok) {
        const errorData = await registerResponse.json()
        throw new Error((errorData as { error?: string }).error || 'Registration failed')
      }
      
      // In development mode, skip email verification and go directly to confirmation
      if (process.env.NODE_ENV === 'development') {
        console.log("Development mode: Skipping email verification");
        setStep("confirmation");
      } else {
        // Production: send the magic link for verification using server action
        const result = await signInWithResend({
          email: formData.email,
          callbackUrl: "/home/<USER>"
        })
        if (result?.error) {
          setError("Failed to send verification email. Please try again.")
        } else {
          setStep("verification")
        }
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Registration failed. Please try again.")
      console.error("Registration error:", err)
    } finally {
      setIsLoading(false)
    }
  }

  const handleConfirmation = () => {
    // Redirect to the app dashboard
    router.push("/home/<USER>")
  }

  return (
    <div className="min-h-screen bg-cream-100 flex flex-col">
      {/* Header */}
      <header className="border-b border-gray-200/50 bg-white">
        <div className="container mx-auto px-4 py-4">
          <Link href="/" className="flex items-center">
            <ArrowLeft className="h-4 w-4 mr-2" />
            <div className="text-forest-600 font-bold text-xl">O6</div>
          </Link>
        </div>
      </header>

      {/* Main Content */}
      <div className="flex-1 flex items-center justify-center p-4">
        <div className="w-full max-w-md">          {step === "account" && (
            <Card className="border-gray-200/50 shadow-sm">
              <CardHeader>
                <CardTitle className="text-2xl">Create your account</CardTitle>
                <CardDescription>
                  Get started with O6 by creating your account. You'll be the owner of your organization.
                </CardDescription>
              </CardHeader>
              <form onSubmit={handleAccountSubmit}>
                <CardContent className="space-y-4">
                  {error && (
                    <Alert variant="destructive">
                      <AlertDescription>{error}</AlertDescription>
                    </Alert>
                  )}
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="firstName">First name</Label>
                      <Input
                        id="firstName"
                        name="firstName"
                        value={formData.firstName}
                        onChange={handleChange}
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="lastName">Last name</Label>
                      <Input id="lastName" name="lastName" value={formData.lastName} onChange={handleChange} required />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="email">Email address</Label>
                    <Input
                      id="email"
                      name="email"
                      type="email"
                      value={formData.email}
                      onChange={handleChange}
                      required
                    />
                  </div>
                </CardContent>
                <CardFooter>
                  <Button type="submit" className="w-full bg-forest-600 hover:bg-forest-700 text-white">
                    Continue
                  </Button>
                </CardFooter>
              </form>
            </Card>
          )}

          {step === "organization" && (
            <Card className="border-gray-200/50 shadow-sm">
              <CardHeader>
                <CardTitle className="text-2xl">Set up your organization</CardTitle>
                <CardDescription>
                  Tell us about your organization. You'll be set up as the owner with full administrative privileges.
                </CardDescription>
              </CardHeader>
              <form onSubmit={handleOrganizationSubmit}>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="organizationName">Organization name</Label>
                    <Input
                      id="organizationName"
                      name="organizationName"
                      value={formData.organizationName}
                      onChange={handleChange}
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="industry">Industry</Label>
                    <select
                      id="industry"
                      name="industry"
                      value={formData.industry}
                      onChange={handleChange}
                      className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                      required
                    >
                      <option value="">Select an industry</option>
                      <option value="technology">Technology</option>
                      <option value="healthcare">Healthcare</option>
                      <option value="finance">Finance</option>
                      <option value="education">Education</option>
                      <option value="manufacturing">Manufacturing</option>
                      <option value="retail">Retail</option>
                      <option value="other">Other</option>
                    </select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="size">Organization size</Label>
                    <select
                      id="size"
                      name="size"
                      value={formData.size}
                      onChange={handleChange}
                      className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                      required
                    >
                      <option value="">Select size</option>
                      <option value="1-10">1-10 employees</option>
                      <option value="11-50">11-50 employees</option>
                      <option value="51-200">51-200 employees</option>
                      <option value="201-500">201-500 employees</option>
                      <option value="501-1000">501-1000 employees</option>
                      <option value="1000+">1000+ employees</option>
                    </select>
                  </div>
                </CardContent>
                <CardFooter className="flex justify-between">
                  <Button
                    type="button"
                    variant="outline"
                    className="border-gray-200 text-gray-700"
                    onClick={() => setStep("account")}
                  >
                    Back
                  </Button>
                  <Button type="submit" className="bg-forest-600 hover:bg-forest-700 text-white">
                    Create Organization
                  </Button>
                </CardFooter>
              </form>
            </Card>
          )}          {step === "verification" && (
            <Card className="border-gray-200/50 shadow-sm">
              <CardHeader className="text-center">
                <div className="mx-auto bg-green-100 w-16 h-16 rounded-full flex items-center justify-center mb-4">
                  <MailCheck className="h-8 w-8 text-green-600" />
                </div>
                <CardTitle className="text-2xl">Check your email</CardTitle>
                <CardDescription>
                  We've sent a verification link to <span className="font-medium">{formData.email}</span>
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <p className="text-center text-sm text-gray-600">
                  Click the link in your email to verify your account and access your O6 dashboard.
                  The link will expire after 15 minutes.
                </p>
                <div className="bg-cream-50 p-4 rounded-lg border border-gray-200/50">
                  <p className="font-medium">{formData.organizationName}</p>
                  <p className="text-sm text-gray-600">
                    Owner: {formData.firstName} {formData.lastName}
                  </p>
                </div>
              </CardContent>
              <CardFooter className="flex flex-col space-y-2">                <Button 
                  type="button"
                  variant="outline"
                  className="w-full border-gray-200 text-gray-700"
                  onClick={async () => {
                    setIsLoading(true);
                    try {
                      await signInWithResend({
                        email: formData.email,
                        callbackUrl: "/home/<USER>"
                      });
                    } catch (err) {
                      console.error("Resend error:", err);
                    } finally {
                      setIsLoading(false);
                    }
                  }}
                  disabled={isLoading}
                >
                  {isLoading ? "Sending..." : "Resend verification link"}
                </Button>
              </CardFooter>
            </Card>
          )}
          
          {step === "confirmation" && (
            <Card className="border-gray-200/50 shadow-sm">
              <CardHeader className="text-center">
                <div className="mx-auto bg-green-100 w-16 h-16 rounded-full flex items-center justify-center mb-4">
                  <CheckCircle className="h-8 w-8 text-green-600" />
                </div>
                <CardTitle className="text-2xl">Organization Created!</CardTitle>
                <CardDescription>
                  Your organization has been successfully created. You are now the owner with full administrative
                  privileges.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="bg-cream-50 p-4 rounded-lg border border-gray-200/50">
                  <p className="font-medium">{formData.organizationName}</p>
                  <p className="text-sm text-gray-600">
                    Owner: {formData.firstName} {formData.lastName}
                  </p>
                  <p className="text-sm text-gray-600">Email: {formData.email}</p>
                </div>
                <p className="text-sm text-gray-600">
                  You can now access your dashboard and start setting up your organization. You can add users, configure
                  roles, and customize your workspace.
                </p>
              </CardContent>
              <CardFooter>
                <Button className="w-full bg-forest-600 hover:bg-forest-700 text-white" onClick={handleConfirmation}>
                  Go to Dashboard
                </Button>
              </CardFooter>
            </Card>
          )}

          <p className="text-center mt-4 text-sm text-gray-600">
            Already have an account?{" "}
            <Link href="/login" className="text-forest-600 hover:underline">
              Log in
            </Link>
          </p>
        </div>
      </div>
    </div>
  )
}


import type React from "react"
import type { <PERSON>ada<PERSON> } from "next"
import { auth } from "@/app/auth"
import { redirect } from "next/navigation";

export const metadata: Metadata = {
  title: "Catalyst - Modern HR & Recruiting Platform",
  description: "Streamline your HR operations, simplify recruiting, and manage your workforce all in one place.",
}

export default async function MarketingLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  // Check authentication state but don't redirect
  const session = await auth();
  
  if (!session) {
    redirect("/login");
  }
    return <>{children}</>
}


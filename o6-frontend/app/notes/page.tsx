import { NotesModule } from "@/components/notes/notes-module"
import { redirect } from 'next/navigation';
import { auth } from "@/app/auth";

export default async function NotesPage() {
  const session = await auth();
  
  // If user is not authenticated, redirect to home page
  if (!session) {
    redirect('/');
  }
  
  return (
    <div className="h-screen">
      <NotesModule user={session.user} />
    </div>
  )
}


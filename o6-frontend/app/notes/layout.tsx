import type React from "react"
import { redirect } from "next/navigation"
import { auth } from "@/app/auth"

export default async function NotesLayout({
  children,
}: {
  children: React.ReactNode
}) {
  // Check authentication server-side for stronger protection
  const session = await auth()
  
  // If no session exists, redirect to login
  if (!session) {
    redirect('/login')
  }
  
  return <div className="h-screen">{children}</div>
}


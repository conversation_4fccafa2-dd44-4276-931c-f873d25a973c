import NextAuth from "next-auth";
import { NextAuthResult } from "next-auth";
import { SupabaseAdapter } from "@auth/supabase-adapter";
import Resend from "next-auth/providers/resend";
import { getCloudflareContext } from "@opennextjs/cloudflare";

const authResult = async (): Promise<NextAuthResult> => {
  // For local development, use a simple configuration without Supabase adapter
  const isLocal = process.env.NODE_ENV === 'development';

  if (isLocal) {
    console.log("Running in local development mode");
    return NextAuth({
      providers: [
        // Temporarily disable providers for local development
        // Email authentication will be mocked in the registration flow
      ],
      callbacks: {
        async session({ session, user }) {
          // Add user data to the session
          if (session.user) {
            session.user.id = user?.id || 'local-user';
          }
          return session;
        }
      },
      pages: {
        signIn: "/login",
        verifyRequest: "/verify-request",
        error: "/auth/error",
        newUser:"/register",
      }
    });
  }

  // For production/Cloudflare deployment
  const context = await getCloudflareContext({async: true});
  const env = context.env;

  return NextAuth({
    providers: [
      Resend({
        apiKey: env.AUTH_RESEND_KEY,
        from: env.AUTH_EMAIL_FROM,
      }),
    ],
    adapter: SupabaseAdapter({
      url: env.SUPABASE_URL,
      secret: env.SUPABASE_SERVICE_ROLE_KEY,
    }),
    callbacks: {
      async session({ session, user }) {
        // Add user data to the session
        if (session.user) {
          session.user.id = user.id;
        }
        return session;
      }
    },
    pages: {
      signIn: "/login",
      verifyRequest: "/verify-request",
      error: "/auth/error",
      newUser:"/register",
    }
  });
};

// Export auth functions without top-level await to avoid async warnings
let authInstance: NextAuthResult | null = null;

const getAuthInstance = async (): Promise<NextAuthResult> => {
  if (!authInstance) {
    authInstance = await authResult();
  }
  return authInstance;
};

export const handlers = {
  GET: async (req: any) => {
    const auth = await getAuthInstance();
    return auth.handlers.GET(req);
  },
  POST: async (req: any) => {
    const auth = await getAuthInstance();
    return auth.handlers.POST(req);
  }
};

export const signIn = async (...args: any[]) => {
  const auth = await getAuthInstance();
  return auth.signIn(...args);
};

export const signOut = async (...args: any[]) => {
  const auth = await getAuthInstance();
  return auth.signOut(...args);
};

export const auth = async (...args: Parameters<NextAuthResult['auth']>) => {
  const authInst = await getAuthInstance();
  return authInst.auth(...args);
};
# Demo Mode

This document explains how to use the DEMO mode feature in the o6-frontend application.

## Overview

DEMO mode allows you to run the application with mock data, which is useful for:

- Demonstrating the application to clients without needing a backend
- Development and testing without a backend dependency
- Preserving all the carefully crafted mock scenarios as we transition to a real backend

## How to Enable Demo Mode

Demo mode is controlled by an environment variable:

```
NEXT_PUBLIC_DEMO_MODE=true
```

This variable is set in the `.env.local` file at the root of the project.

## Implementation Details

The application uses a data service layer (`lib/data-service.ts`) that checks the environment variable to determine whether to use mock data or fetch from a real API.

### Key Components:

1. **Environment Variable**:
   - `NEXT_PUBLIC_DEMO_MODE=true` in `.env.local` enables DEMO mode
   - Setting it to `false` or removing it will prepare the app to use real APIs (once implemented)

2. **Data Service Layer**:
   - Located at `lib/data-service.ts`
   - Provides a consistent interface for data fetching
   - Checks the environment variable to determine the data source
   - When not in DEMO mode, will call the real API endpoints (when implemented)

3. **Mock Data**:
   - All mock data is preserved in `components/workspace/mock-data/`
   - Organized by entity type (programs, objectives, tasks, etc.)

## Using in Different Environments

### For Development:
- Keep `NEXT_PUBLIC_DEMO_MODE=true` to use mock data during development

### For Production with Mock Data (Demo to Clients):
- Build with `NEXT_PUBLIC_DEMO_MODE=true` to create a version that uses mock data

### For Production with Real Backend:
- Once backend is implemented, build with `NEXT_PUBLIC_DEMO_MODE=false` to use real API endpoints

## Future Considerations

As we continue development, we plan to:

1. Gradually replace direct mock data imports with calls to the data service layer
2. Implement real API endpoints while maintaining the ability to switch back to DEMO mode
3. Possibly add UI indicators when in DEMO mode to make it clear to developers/testers

## Additional Notes

- Mock data will be maintained and updated alongside the real implementation
- The data service APIs are designed to be backward compatible as we transition to real APIs 
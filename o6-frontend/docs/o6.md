# O6 Business Suite: Consolidated Technical Design Document

> **Note:** This document builds upon our existing design (*o6-v0.md*) and incorporates enhancements to support a top-level CRM module and reorganize our navigation into two main categories: **HCM** (Recruiting, HR, Benefits, L&D) and **CRM** (Clients, with future expansion to Leads, Opportunities, etc.). The implementation will follow a phased approach.

---

## Table of Contents

- [O6 Business Suite: Consolidated Technical Design Document](#o6-business-suite-consolidated-technical-design-document)
  - [Table of Contents](#table-of-contents)
  - [1. System Overview](#1-system-overview)
  - [2. Architecture](#2-architecture)
    - [2.1 High-Level Architecture](#21-high-level-architecture)
    - [2.2 Data Flow](#22-data-flow)
  - [3. Modules \& Navigation](#3-modules--navigation)
    - [3.1 Top-Level Navigation](#31-top-level-navigation)
    - [3.2 Component Example: App Launcher](#32-component-example-app-launcher)
    - [3.3 Navigation \& Context Awareness](#33-navigation--context-awareness)
      - [Context Switching](#context-switching)
      - [State Management](#state-management)
      - [User Experience](#user-experience)
  - [4. Data Models \& TypeScript Interfaces](#4-data-models--typescript-interfaces)
    - [4.1 Entity Relationship Diagram](#41-entity-relationship-diagram)
    - [4.2 Example TypeScript Interfaces](#42-example-typescript-interfaces)
    - [4.3 Task and Note Templates](#43-task-and-note-templates)
      - [Task Types](#task-types)
      - [Note Templates](#note-templates)
  - [5. UI/UX Design \& Component Structure](#5-uiux-design--component-structure)
    - [5.1 Layout Structure](#51-layout-structure)
    - [5.2 Component Organization](#52-component-organization)
    - [5.3 Sample Component Snippets](#53-sample-component-snippets)
    - [5.4 Task Detail View](#54-task-detail-view)
      - [Component Structure](#component-structure)
      - [Note Templates UI](#note-templates-ui)
      - [User Interaction Flow](#user-interaction-flow)
    - [5.5 Contextual AI Assistant](#55-contextual-ai-assistant)
      - [Module-Specific Persona](#module-specific-persona)
      - [Task-Specific Assistance](#task-specific-assistance)
      - [Implementation](#implementation)
  - [6. Phased Implementation Strategy](#6-phased-implementation-strategy)
    - [Completed](#completed)
    - [In Progress](#in-progress)
    - [Upcoming](#upcoming)
    - [Immediate Priorities](#immediate-priorities)
  - [7. Additional Recommendations](#7-additional-recommendations)
  - [8. Deployment \& CI/CD](#8-deployment--cicd)
    - [8.1 Deployment Process](#81-deployment-process)
    - [8.2 CI/CD Pipeline](#82-cicd-pipeline)
  - [9. Conclusion](#9-conclusion)

---

## 1. System Overview

O6 is a modular, AI-powered business suite designed to manage both Human Capital Management (HCM) and Client Relationship Management (CRM) processes. The platform provides a unified interface where:

- **HCM Modules** (Recruiting, HR, Benefits, L&D) focus on managing the employee lifecycle.
- **CRM Modules** (initially Clients, with plans to expand to Leads, Opportunities, etc.) enable recruitment agencies to manage external client relationships.

Key features include:

- A unified dashboard with role-based access.
- A component-based UI built in Next.js using the App Router.
- An extensible architecture that supports rapid iteration and future enhancements.
- Specialized Task Types with corresponding Note Templates to streamline common workflows.
- Contextual AI Assistant that provides relevant help based on user's current activity.

---

## 2. Architecture

### 2.1 High-Level Architecture

O6 is built using a modern React-based architecture with Next.js and follows a clear separation between frontend, backend, and data storage. The current mock implementation is entirely in Next.js with mock data, transitioning later to a robust backend.

```mermaid
flowchart LR
    A[Client Browser] -- HTTP/HTTPS --> B[Next.js Frontend]
    B -- API Calls (Phase 2+) --> C[Backend API]
    C -- ORM/CRUD --> D[(Relational Database)]
```

- Frontend: Next.js application with a component-based design and App Router.
- Backend: (Phase 2+) API Routes (REST/GraphQL) with authentication and RBAC.
- Database: (Phase 2+) A relational database (e.g., PostgreSQL via Supabase) with multi-tenant support.

### 2.2 Data Flow

Data flows from user interactions in the UI (using React state and context) through API calls (later implemented) and back, ensuring real-time updates and a seamless user experience.

```mermaid
flowchart LR
    U[User Action] -->|Updates| F[Next.js State/Context]
    F -->|Fetches/Updates| A[API Routes]
    A -->|CRUD Operations| D[(Database)]
    D --> A
    A --> F
    F --> U
```

---

## 3. Modules & Navigation

### 3.1 Top-Level Navigation

To support our long-term CRM vision, the global navigation is organized into two primary categories:

- HCM: Contains Recruiting, HR, Benefits, and L&D.
- CRM: Contains Clients (with future expansion to Leads, Opportunities, etc.).

Recommended Navigation Behavior:

- HCM:
  - Clicking HCM reveals a submenu with Recruiting, HR, Benefits, and L&D.
  - Each module retains its internal sidebar and persona-specific views.
- CRM:
  - Clicking CRM reveals a submenu; the first item is Clients.
  - The Clients module is accessible at a dedicated route (e.g., /crm/clients) and designed to eventually incorporate full CRM functionality.

### 3.2 Component Example: App Launcher

The AppLauncher component includes navigation entries for both HCM and CRM. For instance:

```typescript
export const navigationModules = [
  {
    id: "hcm",
    name: "HCM",
    apps: [
      {
        id: "recruiting",
        title: "Recruiting",
        icon: "👥",
        path: "/workspace/recruiting",
        persona: "recruiter",
      },
      {
        id: "hr",
        title: "HR Management",
        icon: "📋",
        path: "/workspace/hr",
        persona: "hr-generalist",
      },
      // Benefits and L&D entries here
    ],
  },
  {
    id: "crm",
    name: "CRM",
    apps: [
      {
        id: "clients",
        title: "Clients",
        icon: "🏢",
        path: "/crm/clients",
        persona: "recruiter",
      },
      // Future CRM features (Leads, Opportunities, etc.)
    ],
  },
];
```

### 3.3 Navigation & Context Awareness

The navigation system can maintain proper context when switching between different modules and views:

#### Context Switching

The application preserves and updates context when switching between:

- Different persona workspaces (Recruiter, HR, Benefits, Learning)
- CRM module (Clients)
- Settings module
- Different views (Dashboard, Timeline, Reports)
- Task details

#### State Management

The WorkspaceModule component manages multiple state variables to track context:

- `activePersona`: Current workspace persona
- `activeItem`: Currently selected workspace item
- `activeTask`: Currently selected task
- `activeClient`: Currently selected client
- `showClientsModule`: Flag for CRM module
- `showSettingsModule`: Flag for Settings module

This state determines what content is rendered and what contextual help is provided by the AI Assistant.

```mermaid
flowchart LR
    A[User] --> B[App Launcher]
    B -->|Select App| C[Workspace Module]
    C --> D{Module Type?}
    D -->|HCM| E[Set activePersona]
    D -->|CRM| F[Set showClientsModule]
    D -->|Settings| G[Set showSettingsModule]
    
    E --> H[Update AI Assistant]
    F --> H
    G --> H
    
    C --> I[Navigation Sidebar]
    I -->|Timeline| J[Timeline View]
    I -->|Dashboard| K[Dashboard View]
    I -->|Reports| L[Reports View]
    
    J -->|Select Task| M[Task Detail View]
    M --> N[Update AI Assistant for Task]
```

#### User Experience

User experience is designed, with the following in mind:

1. Users remain in the correct context when navigating between views
2. The AI Assistant provides relevant help based on the current context
3. Tasks display appropriate templates based on their type
4. Navigation between modules is seamless and maintains state appropriately

---

## 4. Data Models & TypeScript Interfaces

Our data models are extended to support both HCM and CRM functionality.

### 4.1 Entity Relationship Diagram

```mermaid
erDiagram
    ORGANIZATION ||--|{ USER : "has many"
    ORGANIZATION ||--|{ PROGRAM : "has many"
    PROGRAM ||--|{ OBJECTIVE : "has many"
    OBJECTIVE ||--|{ TASK : "has many"
    ORGANIZATION ||--|{ JOB : "has many"
    JOB ||--|{ CANDIDATE : "has many"
    CANDIDATE ||--|{ INTERVIEW_FEEDBACK : "has many"
    ORGANIZATION ||--|{ CLIENT : "has many"
    TASK ||--o| NOTE : "has one"

    USER ||--|| PERSONA : "assigned with"

    ORGANIZATION {
        string id PK
        string name
        %% Additional fields (industry, size, location, etc.)
    }

    USER {
        string id PK
        string orgId FK
        string personaId FK
        string firstName
        string lastName
        string email
    }

    PERSONA {
        string id PK
        string name
        %% Role-based permissions
    }

    PROGRAM {
        string id PK
        string orgId FK
        string title
        string description
        string fiscalYear
    }

    OBJECTIVE {
        string id PK
        string programId FK
        string title
        string description
        Date dueDate
    }

    TASK {
        string id PK
        string objectiveId FK
        string title
        string description
        string status
        string priority
        string assignedTo FK
        string type
        string noteId FK
    }

    NOTE {
        string id PK
        string taskId FK
        string type
        Date lastEdited
    }

    JOB {
        string id PK
        string orgId FK
        %% Optional reference to a Client record
        string clientId FK
        string title
        string description
        string location
        string department
        string salary
        string status
    }

    CLIENT {
        string id PK
        string orgId FK
        string name
        string industry
        string location
        string contactPerson
        string contactEmail
        string contactPhone
        string status
        %% e.g., active, inactive, lead, prospect
        number openJobs
        number totalPlacements
    }

    CANDIDATE {
        string id PK
        string orgId FK
        string jobId FK
        string firstName
        string lastName
        string email
        string phone
        string[] skills
        string status
    }

    INTERVIEW_FEEDBACK {
        string id PK
        string candidateId FK
        string userId FK
        number rating
        string comments
    }
```

### 4.2 Example TypeScript Interfaces

```typescript
export interface Client {
  id: string;
  name: string;
  industry: string;
  location: string;
  contactPerson: string;
  contactEmail: string;
  contactPhone: string;
  status: "active" | "inactive" | "lead" | "prospect";
  openJobs: number;
  totalPlacements: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface Job {
  id: string;
  clientId?: string; // Optional: Only set if job is for a client
  title: string;
  description: string;
  location: string;
  department: string;
  salary: string;
  status: "open" | "closed" | "on_hold";
  createdAt: Date;
  updatedAt: Date;
}

export interface TaskType {
  id: string;
  objectiveId: string;
  title: string;
  description: string;
  dueDate: Date;
  status: "pending" | "in_progress" | "completed";
  priority: "low" | "medium" | "high";
  assignedTo: string;
  relatedItems: string[];
  persona: WorkspacePersona;
  type?: "meeting" | "posting" | "sourcing" | "screening" | "job_description" | "other";
  noteId?: string;
}
```

### 4.3 Task and Note Templates

To enhance the recruitment process, we've implemented specialized task types with corresponding note templates:

```mermaid
classDiagram
    class Task {
        id: string
        objectiveId: string
        title: string
        description: string
        dueDate: Date
        status: string
        priority: string
        assignedTo: string
        type: TaskType
        noteId?: string
    }
    
    class TaskType {
        <<enumeration>>
        meeting
        posting
        sourcing
        screening
        job_description
        other
    }
    
    class Note {
        <<interface>>
        id: string
        taskId: string
        type: string
        lastEdited: Date
    }
    
    class MeetingNote {
        title: string
        date: Date
        attendees: string[]
        roleRequirements: string[]
        compensation: object
        timeline: object
        nextSteps: string[]
    }
    
    class PostingNote {
        title: string
        overview: string
        company: string
        location: string
        salaryRange: string
        experience: string
        responsibilities: string[]
    }
    
    class SourcingNote {
        title: string
        booleanString: string
        analysis: string
        expectedResults: string
    }
    
    class ScreeningNote {
        candidateName: string
        professionalSummary: string
        skills: SkillRating[]
        screeningNotes: string
    }
    
    Task --> TaskType
    Task --> Note
    Note <|-- MeetingNote
    Note <|-- PostingNote
    Note <|-- SourcingNote
    Note <|-- ScreeningNote
```

#### Task Types

Each task in the Recruiting module now belongs to one of four specialized types:

- **Meeting**: For interview planning, client meetings, and team coordination
- **Posting**: For job listing creation and management
- **Sourcing**: For candidate search strategies and boolean search strings
- **Screening**: For candidate evaluation and interview feedback

#### Note Templates

Each task type has a specialized note template with fields relevant to that activity:

- **Meeting Notes**:
  - Meeting date, attendees, role requirements
  - Compensation details (budget, benefits)
  - Timeline information
  - Next steps and action items

- **Posting Notes**:
  - Job overview, company, location
  - Salary range, experience requirements
  - Key responsibilities

- **Sourcing Notes**:
  - Boolean search strings
  - Analysis of sourcing strategy
  - Expected results metrics

- **Screening Notes**:
  - Candidate information
  - Professional summary
  - Skills assessment with ratings
  - Detailed screening feedback

---

## 5. UI/UX Design & Component Structure

### 5.1 Layout Structure

The global layout is consistent across modules and consists of:

- Header: Global navigation and app launcher with persona switching
- Sidebar: Module-specific navigation with persona-aware filtering
- Content Area: Displays the module's detailed views with persona-specific content
- Footer: Common footer across the suite
- Floating Assistant: Contextual AI assistant available on every page

### 5.2 Component Organization

The directory structure has been enhanced to support persona-specific workspaces:

```bash
/app
  ├── crm/                    # CRM module routes
  ├── home/                   # Home dashboard pages
  ├── settings/               # Settings pages
  └── workspace/              # HCM workspace routes

/components
  ├── clients/                # Client-related components
  ├── workspace/              # Enhanced workspace components
  │   ├── ai-assistant/       # AI assistant components
  │   ├── common/             # Shared workspace components
  │   │   ├── workspace-header.tsx
  │   │   ├── workspace-sidebar.tsx
  │   │   └── welcome-screen.tsx
  │   ├── navigation/         # Navigation components
  │   │   ├── top-navigation.tsx
  │   │   ├── module-navigation.tsx
  │   │   └── navigation-data.ts
  │   ├── personas/          # Persona-specific workspaces
  │   │   ├── recruiter/
  │   │   ├── hr-generalist/
  │   │   ├── benefits/
  │   │   └── learning/
  │   ├── task/              # Task-related components
  │   └── views/             # Different view components
  └── ui/                    # Reusable UI components
```

### 5.3 Workspace Module Enhancements

The WorkspaceModule component has been enhanced with persona-specific features:

```typescript
interface WorkspaceModuleProps {
  children?: React.ReactNode
  title?: string
  navigation?: any[]
  module?: string
  initialPersona?: WorkspacePersona
  initialView?: WorkspaceView
}

export function WorkspaceModule({ 
  children, 
  title = "Workspace", 
  navigation = [], 
  module = "home",
  initialPersona = "recruiter",
  initialView = "dashboard"
}: WorkspaceModuleProps) {
  // State management for workspace context
  const [activeItem, setActiveItem] = useState<WorkspaceItem | null>(null)
  const [activeTask, setActiveTask] = useState<TaskType | null>(null)
  const [activeClient, setActiveClient] = useState<Client | null>(null)
  const [activePersona, setActivePersona] = useState<WorkspacePersona>(initialPersona)
  const [activeView, setActiveView] = useState<WorkspaceView>(initialView)
  const [editMode, setEditMode] = useState(false)
  const [showClientsModule, setShowClientsModule] = useState(module === "clients")
  const [showSettingsModule, setShowSettingsModule] = useState(module === "settings")
}
```

Key enhancements include:

1. **Persona-Specific Workspaces**:
   - Each persona (recruiter, hr-generalist, benefits, learning) has its own workspace view
   - Workspace content adapts based on the active persona
   - Navigation and actions are filtered based on persona permissions

2. **Enhanced Navigation**:
   - Top-level navigation with persona switching
   - Module-specific navigation with category filtering
   - Search functionality across workspace items
   - Context-aware sidebar with persona-specific items

3. **Workspace Items**:
   - Support for different item types (job_description, boolean_search, candidate_profile, meeting_notes)
   - Version control for items
   - Status tracking (active, inactive, draft, archived, favorite)
   - Category-based organization

4. **Contextual AI Assistant**:
   - Persona-specific suggestions and actions
   - Task-specific workflow recommendations
   - Item-type specific actions (e.g., posting jobs, searching candidates)
   - Integration with workspace context

### 5.4 Persona-Specific Features

Each persona has specialized features and views:

1. **Recruiter Workspace**:
   - Job description management
   - Boolean search tools
   - Candidate profiles
   - Meeting notes
   - Client relationship tracking

2. **HR Generalist Workspace**:
   - Policy documents
   - Compliance reports
   - Employee records
   - HR documentation

3. **Benefits Workspace**:
   - Benefits plans
   - Enrollment campaigns
   - Coverage documentation
   - Employee benefits tracking

4. **Learning Workspace**:
   - Training programs
   - Learning paths
   - Course materials
   - Progress tracking

### 5.5 Task Management

The task management system has been enhanced with:

1. **Task Types**:
   - Meeting tasks
   - Posting tasks
   - Sourcing tasks
   - Screening tasks
   - Job description tasks

2. **Task Details**:
   - Title and description
   - Due date and priority
   - Status tracking
   - Assignment and ownership
   - Related items and references

3. **Task Notes**:
   - Type-specific note templates
   - Version history
   - Rich text editing
   - AI-assisted content generation

### 5.6 User Experience Improvements

1. **Navigation**:
   - Seamless persona switching
   - Context preservation across views
   - Quick access to frequently used items
   - Search across all workspace content

2. **Content Management**:
   - Drag-and-drop organization
   - Bulk actions
   - Version control
   - Export capabilities

3. **AI Integration**:
   - Contextual suggestions
   - Content generation
   - Workflow automation
   - Smart search

### 5.3 Sample Component Snippets

Workspace Module:

```typescript
export function WorkspaceModule() {
  const [activePersona, setActivePersona] = useState<WorkspacePersona>("recruiter");
  const [activeModule, setActiveModule] = useState<"hcm" | "crm">("hcm");
  const [activeTask, setActiveTask] = useState<TaskType | null>(null);
  const [showClientsModule, setShowClientsModule] = useState(false);
  const [showSettingsModule, setShowSettingsModule] = useState(false);

  const renderWorkspaceContent = () => {
    // If there are children, render those
    if (children) {
      return children
    }
    
    // If a task is selected, show task details with the TaskDetailView component
    if (activeTask) {
      return <TaskDetailView task={activeTask} />
    }

    // If clients module is active, handle different view modes
    if (showClientsModule) {
      if (activeView === "timeline") {
        return <TimelineView activePersona="recruiter" isClientModule={true} onTaskSelect={handleTaskSelect} />
      } else if (activeView === "reports") {
        return <ReportsView activePersona="recruiter" isClientModule={true} />
      } else {
        return <ClientsModule />
      }
    }

    // If settings module should be shown
    if (showSettingsModule) {
      return <SettingsModule />
    }

    // Otherwise show the workspace based on persona and view
    // ...
  };

  return (
    <div className="flex h-screen">
      <Sidebar activeModule={activeModule} />
      <div className="flex-1 flex flex-col">
        <WorkspaceHeader />
        <main className="flex-1 overflow-auto">{renderWorkspaceContent()}</main>
      </div>
      <FloatingAssistant 
        activeItem={activeItem}
        activePersona={activePersona}
        activeTask={activeTask}
        isClientModule={showClientsModule}
        isSettingsModule={showSettingsModule}
      />
    </div>
  );
}
```

App Launcher with Reorganized Navigation:

```typescript
export function AppLauncher({ setActiveModule, setActivePersona }) {
  const navigation = [
    {
      id: "hcm",
      name: "HCM",
      apps: [
        { id: "recruiting", title: "Recruiting", path: "/workspace/recruiting", persona: "recruiter" },
        { id: "hr", title: "HR Management", path: "/workspace/hr", persona: "hr-generalist" },
        // More HCM apps...
      ],
    },
    {
      id: "crm",
      name: "CRM",
      apps: [
        { id: "clients", title: "Clients", path: "/crm/clients", persona: "recruiter" },
        // Future CRM apps...
      ],
    },
  ];

  const handleSelect = (app) => {
    setActiveModule(app.id === "clients" ? "crm" : "hcm");
    setActivePersona(app.persona);
    // Reset task and item state
    setActiveTask(null);
    setActiveItem(null);
    // Navigate to app.path...
  };

  return (
    // Render the app launcher UI with navigation options
  );
}
```

### 5.4 Task Detail View

We've implemented a comprehensive Task Detail View component that provides a rich interface for viewing and editing tasks and their associated notes.

#### Component Structure

The TaskDetailView consists of two main sections:

1. **Task Overview Card**: Displays task metadata (title, description, due date, status, priority, assigned to)
2. **Note Card**: Shows template-specific fields based on task type

```typescript
// Example Component Structure
export function TaskDetailView({ task }: TaskDetailViewProps) {
  const [isEditing, setIsEditing] = useState(false)
  const [note, setNote] = useState<Note | undefined>(getNoteByTaskId(task.id))
  
  // Format the task type for display
  const taskTypeFormatted = task.type ? task.type.charAt(0).toUpperCase() + task.type.slice(1) : "Other"

  // Handle saving note changes
  const handleSaveNote = (updatedNote: Note) => {
    setNote(updatedNote)
    setIsEditing(false)
    // API call would go here in production
  }

  return (
    <div className="space-y-6">
      {/* Task overview card */}
      <Card>
        <CardHeader className="pb-2">
          <CardTitle>{task.title}</CardTitle>
        </CardHeader>
        <CardContent>
          {/* Task details */}
        </CardContent>
      </Card>

      {/* Note card */}
      {note && (
        <Card>
          <CardHeader>
            <CardTitle>{note.type === "screening" ? "Candidate" : "Task"} Notes</CardTitle>
            {/* Edit button */}
          </CardHeader>
          <CardContent>
            {isEditing ? (
              /* Note Editor based on type */
            ) : (
              /* Note Viewer based on type */
            )}
          </CardContent>
        </Card>
      )}
    </div>
  )
}
```

#### Note Templates UI

Each note type has both view and edit modes:

- **View Mode**: Displays the note content in a structured, easy-to-read format
- **Edit Mode**: Provides form fields appropriate to the note type, including:
  - Text inputs for simple fields
  - Textareas for longer content
  - Dynamic arrays for lists (attendees, responsibilities, etc.)
  - Specialized inputs for ratings and structured data

The component also handles empty states by creating appropriate default templates when a task doesn't yet have an associated note.

#### User Interaction Flow

```mermaid
sequenceDiagram
    participant User
    participant TaskView as Task Detail View
    participant NoteComponent as Note Component
    participant Assistant as AI Assistant
    
    User->>TaskView: Select task from Timeline
    TaskView->>NoteComponent: Load note for task ID
    NoteComponent-->>TaskView: Return note or empty template
    TaskView-->>User: Display task details and note
    
    alt Edit Note
        User->>NoteComponent: Click Edit button
        NoteComponent-->>User: Show editable form
        User->>NoteComponent: Make changes
        User->>NoteComponent: Click Save
        NoteComponent->>NoteComponent: Update note state
        NoteComponent-->>User: Show updated view mode
    else Use AI Assistant
        User->>Assistant: Open assistant
        Assistant-->>User: Show task-specific suggestions
        User->>Assistant: Select suggestion
        Assistant->>NoteComponent: Apply suggested content
        NoteComponent-->>User: Show updated note
    end
```

### 5.5 Contextual AI Assistant

The Floating Assistant component is designed to be fully context-aware, providing highly relevant suggestions based on the user's current activity.

```mermaid
flowchart TD
    A[User Context] --> B{Current Module?}
    B -->|Recruiting| C[Recruiter Assistant]
    B -->|HR| D[HR Assistant]
    B -->|Benefits| E[Benefits Assistant]
    B -->|Learning| F[Learning Assistant]
    B -->|Clients| G[Client Assistant]
    B -->|Settings| H[Settings Assistant]
    
    C --> I{Active Task?}
    I -->|No Task| J[General Recruiting Help]
    I -->|Meeting Task| K[Meeting-Specific Help]
    I -->|Posting Task| L[Posting-Specific Help]
    I -->|Sourcing Task| M[Sourcing-Specific Help]
    I -->|Screening Task| N[Screening-Specific Help]
    
    K --> O[Suggested: Meeting Actions]
    K --> P[Draft: Meeting Templates]
    K --> Q[More: Meeting Resources]
```

#### Module-Specific Persona

The assistant now adapts its persona based on the active module:

- **HCM Module**: Shows persona-specific assistants (Recruiter, HR, Benefits, Learning)
- **CRM Module**: Shows the Client Assistant
- **Settings Module**: Shows the Settings Assistant

#### Task-Specific Assistance

When a user is viewing a task, the assistant provides specialized help based on the task type:

1. **Suggested Actions**:
   - Meeting tasks: Generate agenda, create action items, schedule follow-up
   - Posting tasks: Optimize job description, suggest posting channels
   - Sourcing tasks: Improve boolean search, find candidate communities
   - Screening tasks: Generate questions, evaluate candidate fit

2. **Draft Templates**:
   - Meeting tasks: Meeting agenda, summary, action items
   - Posting tasks: Job description, requirements, company overview
   - Sourcing tasks: Boolean search strings, outreach emails
   - Screening tasks: Interview questions, evaluation forms

3. **Resources & Guides**:
   - Meeting tasks: Meeting best practices
   - Posting tasks: Job posting optimization tips
   - Sourcing tasks: Sourcing strategies guide
   - Screening tasks: Interview techniques

#### Implementation

The assistant is implemented as a floating card that maintains awareness of the application state through props:

```typescript
interface FloatingAssistantProps {
  activeItem: WorkspaceItem | null
  activePersona?: WorkspacePersona
  activeClient?: Client | null
  activeTask?: TaskType | null
  isClientModule?: boolean
  isSettingsModule?: boolean
}

export function FloatingAssistant({ 
  activeItem, 
  activePersona = "recruiter",
  activeClient,
  activeTask,
  isClientModule = false,
  isSettingsModule = false
}: FloatingAssistantProps) {
  // Implementation details...
}
```

---

## 6. Phased Implementation Strategy

Our implementation has progressed through several key phases:

### Completed

- ✅ Basic UI/UX with Next.js and mock data
- ✅ Module-based navigation system
- ✅ Implementation of specialized workspaces for different personas
- ✅ Task Detail View with specialized templates
- ✅ Context-aware AI Assistant
- ✅ CRM module basic implementation
- ✅ Settings module basic implementation

### In Progress

- 🔄 Enhanced Timeline View with hierarchical data
- 🔄 Specialized reporting for different modules
- 🔄 Complete client management functionality

### Upcoming

- 📅 Backend API implementation
- 📅 Authentication and authorization
- 📅 Database integration
- 📅 Advanced analytics and reporting

### Immediate Priorities

1. Complete specialized task templates for all personas
2. Enhance AI Assistant capabilities with more actionable suggestions
3. Implement create/edit functionality for all entity types
4. Add advanced filtering and search capabilities

The original four-phase plan remains our strategic roadmap:

Phase 1: Next.js Mock Data (UI/UX Validation) - **Largely Complete**

- Implementation:
  - Build all pages using static/mock JSON data stored in /components/workspace/mock-data.
  - Implement navigation changes so that the global menu now shows two categories: HCM and CRM.
  - Use React Context and local state to simulate data changes.

Phase 2: Real Backend, Database, & Authentication - **Upcoming**

- Objective: Replace mock data with a full backend and persistent storage.
- Implementation:
  - Develop API routes in Next.js for CRUD operations on Organizations, Users, Jobs, Clients, etc.
  - Integrate a relational database (e.g., Supabase with PostgreSQL) with multi-tenant support.
  - Implement authentication (Supabase Auth or similar) and RBAC to secure access based on persona.
  - Update Next.js pages to fetch data from the backend.

Phase 3 & 4: As originally planned.

---

## 7. Additional Recommendations

- Navigation Refinement:
  - Ensure that users only see navigation items that are relevant to their roles. For example, only show CRM items to users with recruiter or account management roles.
  - Maintain context between app switches to prevent data loss.
  
- Modular Code Organization:
  - Maintain clear separation between HCM and CRM codebases to facilitate independent development and testing.
  - Use shared components for similar functionality (like the TaskDetailView).
  
- Task Template System:
  - Consider making the task template system extensible so that new task types can be added easily.
  - Allow organizations to customize their task type templates.
  
- AI Assistant Enhancement:
  - Continue improving task-specific suggestions to make them more relevant.
  - Add features to auto-generate content for notes based on task context.
  - Implement an action system where the assistant can perform actions like scheduling meetings.

- Future-Proofing:
  - Design APIs and data models with extensibility in mind to easily add new fields or modules as the suite grows.
  
- Testing & Documentation:
  - Invest in unit, integration, and end-to-end tests to maintain system stability.
  - Continuously update documentation to reflect changes as features evolve.

---

## 8. Deployment & CI/CD

### 8.1 Deployment Process

- Cloud Deployment:
  - The app is deployed to Cloud Provider with automatic builds on GitHub pushes.
  - Preview deployments are created for every pull request, ensuring that new features can be tested in isolation.

### 8.2 CI/CD Pipeline

- Automated Testing:
  - Set up automated unit and integration tests to run on every commit.
- Environment Configuration:
  - Use environment variables for API endpoints, authentication keys, and feature flags.

```bash
NEXT_PUBLIC_SUPABASE_URL=
NEXT_PUBLIC_SUPABASE_ANON_KEY=
DATABASE_URL=
AI_API_KEY=
NEXT_PUBLIC_ENABLE_AI_FEATURES=true
```

---

## 9. Conclusion

This consolidated design document outlines our roadmap for evolving the O6 Business Suite into a robust, modular platform supporting both HCM and CRM functionalities. With a clear phased approach—from Next.js mock data to a full backend implementation and eventual advanced analytics—we are well positioned to meet the needs of both internal HR management and external client relationship management.

Our key improvements include:

- Reorganizing the global navigation to separate HCM and CRM.
- Adding a dedicated Clients module with plans for future CRM expansion.
- Implementing specialized Task Types and Note Templates for the recruitment process.
- Developing a context-aware AI Assistant that provides relevant help based on user activity.
- Creating a comprehensive Task Detail View that combines task information with type-specific notes.
- Enhancing navigation with improved context awareness and state management.

These enhancements create a more robust and user-friendly system that better supports the specialized workflows of recruiting and HR professionals.

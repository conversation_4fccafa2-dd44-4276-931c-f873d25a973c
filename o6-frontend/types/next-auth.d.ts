import NextAuth from "next-auth";

declare module "next-auth" {
  /**
   * Extend the built-in session types
   */  interface Session {
    sessionToken?: string | null;
    user?: {
      name?: string | null;
      email?: string | null;
      image?: string | null;
      id: string;
    };
  }

  /**
   * Extend the built-in user types
   */
  interface User {
    id: string;
    name?: string | null;
    email?: string | null;
    image?: string | null;
  }
}
